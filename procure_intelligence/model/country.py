from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class Country:
    """
    Represents a country with trade data and tariff information.
    """
    name: str
    transaction_count: int
    export_volume_tons: float
    avg_price_per_ton: float
    total_export_value: float
    unit: str = "KGS"
    from_llm: bool = False
    
    # Tariff information
    duty: Optional[str] = None
    footer_duty: Optional[str] = None
    new_tariff: Optional[str] = None
    ad: Optional[str] = None  # Anti-dumping duty
    cvd: Optional[str] = None  # Countervailing duty
    total_duty: Optional[str] = None
    proposed_tariff: Optional[str] = None
    duty_percentage: Optional[float] = None
    
    # Ranking information
    rank: Optional[int] = None
    score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the country object to a dictionary."""
        return {
            "country": self.name,
            "transaction_count": self.transaction_count,
            "export_volume_tons": self.export_volume_tons,
            "avg_price_per_ton": self.avg_price_per_ton,
            "total_export_value": self.total_export_value,
            "unit": self.unit,
            "from_llm": self.from_llm,
            "duty": self.duty,
            "footer_duty": self.footer_duty,
            "new_tariff": self.new_tariff,
            "ad": self.ad,
            "cvd": self.cvd,
            "total_duty": self.total_duty,
            "proposed_tariff": self.proposed_tariff,
            "duty_percentage": self.duty_percentage,
            "Rank": self.rank,
            "Score": self.score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Country':
        """Create a Country object from a dictionary."""
        # Calculate total value if not provided
        total_value = data.get("total_export_value")
        if total_value is None:
            total_value = data.get("export_volume_tons", 0) * data.get("avg_price_per_ton", 0)
            
        return cls(
            name=data.get("country", "") or data.get("origin_country", "Unknown"),
            transaction_count=data.get("transaction_count", 0) or data.get("shipment_count", 0),
            export_volume_tons=data.get("export_volume_tons", 0) or data.get("total_quantity", 0),
            avg_price_per_ton=data.get("avg_price_per_ton", 0) or data.get("average_fob", 0),
            total_export_value=total_value,
            unit=data.get("unit", "KGS"),
            from_llm=data.get("from_llm", False),
            duty=data.get("duty"),
            footer_duty=data.get("footer_duty"),
            new_tariff=data.get("new_tariff"),
            ad=data.get("ad"),
            cvd=data.get("cvd"),
            total_duty=data.get("total_duty"),
            proposed_tariff=data.get("proposed_tariff"),
            duty_percentage=data.get("duty_percentage"),
            rank=data.get("Rank"),
            score=data.get("Score")
        )


@dataclass
class CountryLookupResult:
    """
    Represents the result of a country lookup operation.
    """
    status: str  # "success", "error", "warning"
    message: Optional[str] = None
    data: Optional[List[Dict[str, Any]]] = None
