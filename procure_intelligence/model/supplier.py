from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class Supplier:
    """
    Represents a supplier with trade data and contact information.
    """
    name: str
    country: str
    transaction_count: int
    export_volume_tons: float
    avg_price_per_ton: float
    total_export_value: float
    unit: str = "KGS"
    global_exporter_id: Optional[str] = None
    average_quantity_per_shipment: Optional[float] = None
    from_llm: bool = False
    
    # Additional details that might be populated later
    contact_info: Optional[Dict[str, Any]] = None
    product_quality: Optional[str] = None
    certifications: Optional[List[str]] = None
    advantages: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the supplier object to a dictionary."""
        return {
            "name": self.name,
            "country": self.country,
            "transaction_count": self.transaction_count,
            "export_volume_tons": self.export_volume_tons,
            "avg_price_per_ton": self.avg_price_per_ton,
            "total_export_value": self.total_export_value,
            "unit": self.unit,
            "global_exporter_id": self.global_exporter_id,
            "average_quantity_per_shipment": self.average_quantity_per_shipment,
            "from_llm": self.from_llm,
            "contact_info": self.contact_info,
            "product_quality": self.product_quality,
            "certifications": self.certifications,
            "advantages": self.advantages
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], country: str = "") -> 'Supplier':
        """Create a Supplier object from a dictionary."""
        # Calculate derived values if not provided
        avg_quantity = data.get("average_quantity_per_shipment")
        if avg_quantity is None and data.get("transaction_count", 0) > 0:
            avg_quantity = data.get("export_volume_tons", 0) / data.get("transaction_count", 1)
            
        total_value = data.get("total_export_value")
        if total_value is None:
            total_value = data.get("export_volume_tons", 0) * data.get("avg_price_per_ton", 0)
            
        return cls(
            name=data.get("name", "Unknown Supplier"),
            country=data.get("country", country),
            transaction_count=data.get("transaction_count", 0),
            export_volume_tons=data.get("export_volume_tons", 0),
            avg_price_per_ton=data.get("avg_price_per_ton", 0),
            total_export_value=total_value,
            unit=data.get("unit", "KGS"),
            global_exporter_id=data.get("global_exporter_id", ""),
            average_quantity_per_shipment=avg_quantity,
            from_llm=data.get("from_llm", False),
            contact_info=data.get("contact_info"),
            product_quality=data.get("product_quality"),
            certifications=data.get("certifications"),
            advantages=data.get("advantages")
        )


@dataclass
class SupplierLookupResult:
    """
    Represents the result of a supplier lookup operation.
    """
    status: str  # "success", "error", "warning"
    message: Optional[str] = None
    data: Optional[List[Dict[str, Any]]] = None
