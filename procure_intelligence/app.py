import streamlit as st
import pandas as pd
import requests
import json
import plotly.express as px
import plotly.graph_objects as go
import os
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import services and controllers
from service.client.llm_client import LLMClient
from service.client.db_client import DatabaseClient
from service.chemical_service import ChemicalService
from service.country_service import CountryService
from service.supplier_service import SupplierService
from service.ranking_service import RankingService
from controller.chemical_controller import ChemicalController
from controller.country_controller import CountryController
from controller.supplier_controller import SupplierController
from utils.formatters import format_number

# The app displays suppliers in two sections:
# Database suppliers from the API
# LLM-generated suppliers

# Load environment variables from .env file if it exists
load_dotenv()

# Set page configuration
st.set_page_config(
    page_title="Chemical Supplier Finder",
    page_icon="🧪",
    layout="wide"
)

# Initialize services with LLM clients
@st.cache_resource
def initialize_services():
    """Initialize all services with proper LLM clients."""
    # Get API keys from environment
    perplexity_api_key = os.getenv("PERPLEXITY_API_KEY")
    openai_api_key = os.getenv("OPENAI_API_KEY")

    # Initialize LLM client
    llm_client = LLMClient(perplexity_api_key, openai_api_key)

    # Initialize database client (placeholder for now)
    db_config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', 5432),
        'database': os.getenv('DB_NAME', 'volza_trade_data'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'postgres')
    }
    db_client = DatabaseClient(db_config)

    # Initialize services with LLM client
    chemical_service = ChemicalService(llm_client, historical_data_path="data/historical_data.csv")
    country_service = CountryService(db_client, llm_client)
    supplier_service = SupplierService(db_client, llm_client)
    ranking_service = RankingService()

    # Initialize controllers
    chemical_controller = ChemicalController(chemical_service)
    country_controller = CountryController(country_service, ranking_service)
    supplier_controller = SupplierController(supplier_service, ranking_service)

    return {
        'chemical_controller': chemical_controller,
        'country_controller': country_controller,
        'supplier_controller': supplier_controller,
        'llm_client': llm_client
    }

# Get initialized services
services = initialize_services()

# Custom CSS for styling
st.markdown("""
    <style>
    .main {
        padding: 2rem;
    }
    .stApp {
        max-width: 1200px;
        margin: 0 auto;
    }
    .title-container {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .card {
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    .supplier-card {
        border-left: 4px solid #4CAF50;
    }
    .llm-data-box {
        background-color: #f8f9fa;
        border: 2px dashed #6c757d;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .llm-data-header {
        background-color: #e9ecef;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: inline-block;
    }
    .supplier-details-container {
        background-color: #f8f9fa;
        border: 2px solid #4CAF50;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .supplier-details-header {
        background-color: #4CAF50;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-weight: bold;
        margin-bottom: 1rem;
        display: inline-block;
    }
    .supplier-section {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    .supplier-section-title {
        font-weight: bold;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .supplier-db-section {
        background-color: #e7f3ff;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 5px;
    }
    .supplier-llm-section {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 5px;
    }
    .select-button {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
    }
    .select-button:hover {
        background-color: #45a049;
    }
    </style>
""", unsafe_allow_html=True)

st.image("mstackLogo.png", width=150)  # Adjust width as needed
st.markdown("---")


def api_get_hs_codes(chemical_name, application, category=None):
    """Get HS codes for a given chemical and its application.

    Priority order:
    1. Historical data (local CSV file)
    2. External API
    3. LLM fallback

    Args:
        chemical_name (str): The name of the chemical
        application (str): The application of the chemical
        category (str, optional): The category of the chemical

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import json

    # Sanitize input - remove trailing commas and extra spaces
    chemical_name = chemical_name.strip().rstrip(',').strip()
    if application:
        application = application.strip().rstrip(',').strip()
    if category:
        category = category.strip().rstrip(',').strip()

    # STEP 1: First, try local historical data
    print(f"Step 1: Checking historical data for '{chemical_name}'...")
    try:
        chemical_controller = services['chemical_controller']
        # Check only historical data by calling the service directly
        chemical_service = chemical_controller.chemical_service

        # Check if chemical exists in historical data
        matching_rows = chemical_service.historical_df.loc[chemical_service.historical_df['product_name'] == chemical_name]

        if not matching_rows.empty:
            hs_code = matching_rows['hs_code'].values[0]
            output = {
                "status": "success",
                "data": [
                    {
                        "hs_code": hs_code,
                        "product_family": [chemical_name],
                        "description": f"{chemical_name} - {application or 'General'}"
                    }
                ]
            }
            print(f"✅ Step 1 SUCCESS - Historical Data: returning output:{output}")
            return output
        else:
            print(f"❌ Step 1 FAILED - Not found in historical data, trying external API...")

    except Exception as e:
        print(f"❌ Step 1 ERROR - Historical data check failed: {str(e)}, trying external API...")

    # STEP 2: Try external API
    print(f"Step 2: Calling external API for '{chemical_name}'...")
    api_url = "http://************:8081/api/chemical-lookup"

    # Prepare payload
    payload = {
        "chemical_name": chemical_name.upper()
    }

    # Add application if provided
    if application and application.strip():
        payload["chemical_application"] = application

    # Add category if provided
    if category and category.strip():
        payload["chemical_category"] = category

    # Headers for API request
    headers = {
        "Content-Type": "application/json"
    }

    try:
        # Make API request
        response = requests.post(api_url, headers=headers, json=payload)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()
            print(f"External API response: {api_response}")

            # Format in expected structure
            if "hs_code" in api_response and api_response["hs_code"]:
                # Handle both string and list responses for hs_code
                hs_code = api_response["hs_code"]
                if isinstance(hs_code, list):
                    hs_code = hs_code[0] if hs_code else ""

                # Check if we have a valid HS code (not empty or error)
                if not hs_code or (isinstance(hs_code, str) and (hs_code.lower() in ['error', 'not found', ''] or not hs_code.strip())):
                    print(f"❌ Step 2 FAILED - External API returned invalid HS code: '{hs_code}', trying LLM fallback...")
                else:
                    # Handle product_family (can be string or list)
                    product_family = api_response.get("product_family", [])
                    if isinstance(product_family, str):
                        if product_family in ["Not found", ""] or not product_family.strip():
                            product_family = [chemical_name]
                        else:
                            product_family = [product_family]
                    elif isinstance(product_family, list):
                        if not product_family or (len(product_family) == 1 and product_family[0] in ["Not found", ""]):
                            product_family = [chemical_name]
                    else:
                        product_family = [chemical_name]

                    output= {
                        "status": "success",
                        "data": [
                            {
                                "hs_code": hs_code,
                                "product_family": product_family,
                                "description": f"{chemical_name} - {application or 'General'}"
                            }
                        ]
                    }
                    print(f"✅ Step 2 SUCCESS - External API: returning output:{output}")
                    return output
            else:
                print(f"❌ Step 2 FAILED - External API returned no valid HS code, trying LLM fallback...")
        else:
            print(f"❌ Step 2 FAILED - External API status {response.status_code}, trying LLM fallback...")

    except Exception as e:
        print(f"❌ Step 2 ERROR - External API failed: {str(e)}, trying LLM fallback...")

    # STEP 3: LLM fallback
    print(f"Step 3: Using LLM fallback for '{chemical_name}'...")
    try:
        chemical_controller = services['chemical_controller']
        chemical_service = chemical_controller.chemical_service

        # Call the LLM directly (skip historical data check since we already did that)
        llm_result = chemical_service._compute_catalog(chemical_name, application)

        if "error" not in llm_result and llm_result.get("hs_code"):
            output = {
                "status": "success",
                "data": [
                    {
                        "hs_code": llm_result["hs_code"],
                        "product_family": [chemical_name],
                        "description": f"{chemical_name} - {application or 'General'}"
                    }
                ]
            }
            print(f"✅ Step 3 SUCCESS - LLM Fallback: returning output:{output}")
            return output
        else:
            output = {
                "status": "error",
                "message": f"All methods failed. Historical: not found, External API: failed, LLM: {llm_result.get('error', 'Unknown LLM error')}"
            }
            print(f"❌ Step 3 FAILED - All methods exhausted: returning output:{output}")
            return output

    except Exception as local_e:
        output = {
            "status": "error",
            "message": f"All methods failed. Historical: not found, External API: failed, LLM: {str(local_e)}"
        }
        print(f"❌ Step 3 ERROR - LLM fallback failed: returning output:{output}")
        return output

def api_get_top_countries_tariff(hs_code, chemical_name, destination, months=12):
    """Get top countries exporting a product with given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        chemical_name (str, optional): The name of the chemical
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers_by_geography_tariff"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "destination":destination,
        "months": months
    }

    # Add chemical name if provided
    if chemical_name:
        params["chemical_name"] = chemical_name

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])
                print(api_data)

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Parse tariff_info JSON string if available
                    tariff_info_str = item.get("tariff_info")
                    try:
                        tariff_info = json.loads(tariff_info_str) if tariff_info_str else {}
                    except json.JSONDecodeError:
                        tariff_info = {}

                    # Extract useful tariff fields
                    duty = tariff_info.get("duty", "N/A")
                    footer_duty = tariff_info.get("footer_duty", "N/A")
                    new_tariff = tariff_info.get("New Tariff", "N/A")
                    ad = tariff_info.get("Anti-Dumping Duty", "N/A")
                    cvd = tariff_info.get("Countervailing Duty", "N/A")
                    total_duty = tariff_info.get("Total Duty", "N/A")
                    proposed_tariff = tariff_info.get("Proposed Tariff", "N/A")
                    # Format the data to match the expected structure
                    # Format final structure
                    transformed_item = {
                        "country": item.get("origin_country", "Unknown"),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_value,
                        "unit": item.get("global_std_unit_id", "KGS"),
                        "duty": duty,
                        "footer_duty": footer_duty,
                        "new_tariff": new_tariff,
                        "ad": ad,
                        "cvd": cvd,
                        "total_duty": total_duty,
                        "proposed_tariff": proposed_tariff
                    }
                    transformed_data.append(transformed_item)

                # If no data was found, return some default data for testing
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": "No supplier countries found for the given HS code.",
                        "data": []
                    }

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }

def api_get_top_countries(hs_code, chemical_name, destination, months=12):
    """Get top countries exporting a product with given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        chemical_name (str, optional): The name of the chemical
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    print("*****")
    print(hs_code, chemical_name, destination, months)
    print("*****")
    #exit(1)
    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers_by_geography"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "destination": destination,
        "months": months
    }

    # Add chemical name if provided
    if chemical_name:
        params["chemical_name"] = chemical_name

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Format the data to match the expected structure
                    transformed_item = {
                        "country": item.get("origin_country", "Unknown"),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_value,
                        "unit": item.get("global_std_unit_id", "KGS")
                    }

                    transformed_data.append(transformed_item)

                # If no data was found, return some default data for testing
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": "No supplier countries found for the given HS code.",
                        "data": []
                    }

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }

def api_get_top_suppliers(hs_code, country, months=12):
    """Get top suppliers from a specific country for a given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        country (str): The origin country to search for suppliers
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "origin": country,
        "months": months
    }

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_export_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Format the data to match the expected structure
                    transformed_item = {
                        "name": item.get("exporter_name", "Unknown Supplier"),
                        "global_exporter_id": item.get("global_exporter_id", ""),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_export_value,
                        "unit": item.get("global_std_unit_id", "KGS"),
                        "average_quantity_per_shipment": item.get("average_quantity_per_shipment", 0) or 0
                    }

                    transformed_data.append(transformed_item)

                # If no data was found, return an informative message
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": f"No suppliers found in {country} for the given HS code.",
                        "data": []
                    }

                # Check if any suppliers have missing names
                missing_names = False
                for supplier in transformed_data:
                    if supplier.get('name') == "-" or not supplier.get('name') or supplier.get('name', "").strip() == "":
                        missing_names = True
                        break

                # If we have missing names and we have the chemical name in session state, pre-fetch them
                if missing_names and hasattr(st.session_state, 'chemical_name') and st.session_state.chemical_name:
                    # Pre-fetch supplier names using LLM
                    prefetched_suppliers = prefetch_supplier_names(
                        st.session_state.chemical_name,
                        hs_code,
                        country,
                        len(transformed_data)
                    )

                    # If we got valid prefetched data, update the supplier names
                    if prefetched_suppliers["status"] == "success" and prefetched_suppliers["data"]:
                        for i, supplier in enumerate(transformed_data):
                            if supplier.get('name') == "-" or not supplier.get('name') or supplier.get('name', "").strip() == "":
                                # Try to get a name from the prefetched data
                                if i < len(prefetched_suppliers["data"]):
                                    supplier['name'] = prefetched_suppliers["data"][i].get('name', supplier.get('name', "-"))

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }

# Helper functions for visualization
def format_number(num):
    """Format large numbers with commas and truncate to 2 decimal places."""
    if isinstance(num, float):
        num = int(num * 100) / 100  # Truncate to 2 decimal places
        return f"{num:,.2f}"
    return f"{num:,}"

def create_country_comparison_chart(countries_data):
    """Create a bar chart comparing countries."""
    df = pd.DataFrame(countries_data)

    fig = px.bar(
        df,
        x='country',
        y='export_volume_tons',
        color='country',
        labels={'export_volume_tons': 'Export Volume (tons)', 'country': 'Country'},
        title='Export Volume by Country',
        text_auto=True
    )

    fig.update_layout(height=400)
    return fig

def create_country_map_chart(countries_data):
    """Create a choropleth map visualizing countries by export volume."""
    df = pd.DataFrame(countries_data)

    # Ensure country names are standardized for the map
    # This maps common country name variations to ISO standard names
    country_name_map = {
        'United States': 'USA',
        'USA': 'USA',
        'US': 'USA',
        'United Kingdom': 'GBR',
        'UK': 'GBR',
        'China': 'CHN',
        'India': 'IND',
        'Germany': 'DEU',
        'France': 'FRA',
        'Japan': 'JPN',
        'South Korea': 'KOR',
        'Korea': 'KOR',
        'Russia': 'RUS',
        'Brazil': 'BRA',
        'Canada': 'CAN',
        'Australia': 'AUS',
        'Italy': 'ITA',
        'Spain': 'ESP',
        'Mexico': 'MEX',
        'Indonesia': 'IDN',
        'Netherlands': 'NLD',
        'Saudi Arabia': 'SAU',
        'Turkey': 'TUR',
        'Switzerland': 'CHE',
        'Taiwan': 'TWN',
        'Sweden': 'SWE',
        'Poland': 'POL',
        'Belgium': 'BEL',
        'Thailand': 'THA',
        'Ireland': 'IRL',
        'Austria': 'AUT',
        'Singapore': 'SGP',
        'Vietnam': 'VNM',
        'Malaysia': 'MYS',
        'UAE': 'ARE',
        'United Arab Emirates': 'ARE'
    }

    # Create a new column with standardized country codes
    df['iso_alpha'] = df['country'].map(lambda x: country_name_map.get(x, x))

    # Create the choropleth map
    fig = px.choropleth(
        df,
        locations='iso_alpha',
        color='export_volume_tons',
        hover_name='country',
        hover_data={
            'iso_alpha': False,
            'export_volume_tons': True,
            'transaction_count': True,
            'avg_price_per_ton': True
        },
        color_continuous_scale='Viridis',
        labels={
            'export_volume_tons': 'Export Volume (tons)',
            'transaction_count': 'Transactions',
            'avg_price_per_ton': 'Avg. Price per Ton (USD)'
        },
        title='Global Export Volume by Country',
        projection='natural earth'
    )

    fig.update_layout(
        height=500,
        margin={"r":0,"t":50,"l":0,"b":0},
        coloraxis_colorbar=dict(
            title="Export Volume (tons)"
        )
    )

    return fig

def create_supplier_comparison_chart(suppliers_data):
    """Create a bar chart comparing suppliers."""
    df = pd.DataFrame(suppliers_data)

    fig = px.bar(
        df,
        x='name',
        y='export_volume_tons',
        color='name',
        labels={'export_volume_tons': 'Export Volume (tons)', 'name': 'Supplier'},
        title='Export Volume by Supplier',
        text_auto=True
    )

    fig.update_layout(height=400)
    return fig

def create_price_comparison_chart(data, x_field, name_field):
    """Create a horizontal bar chart for price comparison."""
    df = pd.DataFrame(data)

    fig = px.bar(
        df,
        y=name_field,
        x='avg_price_per_ton',
        orientation='h',
        labels={'avg_price_per_ton': 'Avg. Price per Ton (USD)', name_field: x_field},
        title=f'Average Price Comparison',
        color='avg_price_per_ton',
        color_continuous_scale='Viridis',
        text_auto=True
    )

    fig.update_layout(height=300)
    return fig

def generate_llm_data(hs_code, chemical_name, destination, is_tariff=False):
    """Generate data using the service layer with LLM for top exporting countries.

    Args:
        hs_code (str): The HS code to search for
        chemical_name (str): The name of the chemical
        destination (str): The destination country
        is_tariff (bool): Whether to include tariff information

    Returns:
        dict: A dictionary with status and data fields containing LLM-generated data
    """
    # Ensure we have valid inputs
    if not hs_code:
        hs_code = "2915.90"  # Default HS code if none provided

    if not chemical_name:
        chemical_name = "Generic Chemical"  # Default chemical name if none provided

    if not destination:
        destination = "United States"  # Default destination if none provided

    try:
        # Use the LLM client from the initialized services
        llm_client = services['llm_client']
        result = llm_client.generate_country_data(hs_code, chemical_name, destination, is_tariff)

        return {
            "status": result["status"],
            "message": result["message"],
            "data": result["data"]
        }
    except Exception as e:
        logger.error(f"Error generating LLM data: {str(e)}")
        return {
            "status": "error",
            "message": f"Error generating LLM data: {str(e)}",
            "data": []
        }

def generate_generic_suppliers(country, count=5):
    """Generate generic supplier data when real data cannot be obtained.

    Args:
        country (str): The country for which to generate suppliers
        count (int): Number of suppliers to generate

    Returns:
        list: A list of dictionaries containing generic supplier data
    """
    import random

    # List of generic company name patterns
    company_patterns = [
        f"{country} Chemical Co., Ltd.",
        f"{country} Industrial Chemicals",
        f"Global Chemicals ({country})",
        f"{country} Chemical Industries",
        f"International Chemicals {country}",
        f"{country} Chemical Exports",
        f"Chemical Solutions {country}",
        f"{country} Chemical Manufacturing",
        f"United Chemicals {country}",
        f"{country} Chemical Trading Co."
    ]

    # Generate data for each supplier
    supplier_data = []

    for i in range(min(count, len(company_patterns))):
        # Generate realistic-looking data
        transaction_count = random.randint(50, 500)
        export_volume = random.uniform(100, 2000)
        avg_price = random.uniform(10, 200)
        total_value = export_volume * avg_price

        supplier_data.append({
            "name": company_patterns[i],
            "global_exporter_id": f"LLM-{i+1}",
            "transaction_count": transaction_count,
            "export_volume_tons": export_volume,
            "avg_price_per_ton": avg_price,
            "total_export_value": total_value,
            "unit": "KGS",
            "average_quantity_per_shipment": export_volume / transaction_count,
            "from_llm": True
        })

    return supplier_data

def get_real_suppliers_with_llm(chemical_name, hs_code, country):
    """Get real supplier data using the service layer with LLM.

    Args:
        chemical_name (str): The name of the chemical
        hs_code (str): The HS code of the product
        country (str): The country of the suppliers

    Returns:
        dict: A dictionary with status and data fields containing LLM-generated supplier data
    """
    try:
        # Use the LLM client from the initialized services
        llm_client = services['llm_client']
        result = llm_client.generate_supplier_data(chemical_name, hs_code, country)

        return {
            "status": result["status"],
            "message": result["message"],
            "data": result["data"]
        }
    except Exception as e:
        logger.error(f"Error generating supplier data: {str(e)}")
        return {
            "status": "error",
            "message": f"Error generating supplier data: {str(e)}",
            "data": []
        }

def prefetch_supplier_names(chemical_name, hs_code, country, count=5):
    """Prefetch supplier names for a given chemical, HS code, and country.

    This function is used to get real supplier names when the API returns "-" or empty names.

    Args:
        chemical_name (str): The name of the chemical
        hs_code (str): The HS code of the product
        country (str): The country of the suppliers
        count (int): Number of suppliers to fetch

    Returns:
        dict: A dictionary with status and data fields containing supplier names
    """
    # Use the existing get_real_suppliers_with_llm function to fetch supplier data
    return get_real_suppliers_with_llm(chemical_name, hs_code, country)

def main():
    """Main application function."""
    # Initialize session state
    if 'step' not in st.session_state:
        st.session_state.step = 1
    if 'hs_codes' not in st.session_state:
        st.session_state.hs_codes = None
    if 'selected_hs_code' not in st.session_state:
        st.session_state.selected_hs_code = None
    if 'countries_data' not in st.session_state:
        st.session_state.countries_data = None
    if 'llm_countries_data' not in st.session_state:
        st.session_state.llm_countries_data = None
    if 'selected_country' not in st.session_state:
        st.session_state.selected_country = None
    if 'suppliers_data' not in st.session_state:
        st.session_state.suppliers_data = None
    if 'db_suppliers_data' not in st.session_state:
        st.session_state.db_suppliers_data = None
    if 'llm_suppliers_data' not in st.session_state:
        st.session_state.llm_suppliers_data = None
    if 'using_llm_data' not in st.session_state:
        st.session_state.using_llm_data = False
    if 'tariff' not in st.session_state:
        st.session_state.tariff = 0
    if 'perplexity_api_key' not in st.session_state:
        # Initialize with empty string
        st.session_state.perplexity_api_key = ""
    if 'selected_supplier' not in st.session_state:
        st.session_state.selected_supplier = None
    if 'supplier_details' not in st.session_state:
        st.session_state.supplier_details = None
    if 'show_supplier_details' not in st.session_state:
        st.session_state.show_supplier_details = False

    # Get Perplexity API key from environment variable
    perplexity_api_key = os.getenv("PERPLEXITY_API_KEY", "")
    if perplexity_api_key:
        st.session_state.perplexity_api_key = perplexity_api_key

    # Step 1: Enter chemical name and application
    if st.session_state.step == 1:
        st.subheader("Step 1: Enter Chemical Information")

        col1, col2, col3 = st.columns(3)
        with col1:
            chemical_name = st.text_input("Chemical Name", placeholder="e.g., EPOXIDE RESINS")
        with col2:
            application = st.text_input("Category", placeholder="e.g., Coatings")
        with col3:
            destination = st.selectbox("Destination", ['United States','China','India','Saudi Arabia','UAE','Indonesia','Mexico'])

        if destination == 'United States':
            st.session_state.tariff = 1
        else:
            st.session_state.tariff = 0

        st.session_state.destination = destination

        if st.button("Search HS Codes", type="primary", use_container_width=True):
            if chemical_name:
                st.session_state.chemical_name=chemical_name
                with st.spinner("Fetching HS codes..."):
                    # Call API to get HS codes with all parameters
                    response = api_get_hs_codes(chemical_name, application, application)
                    if response["status"] == "success" and response["data"]:
                        # Ensure hs_codes is always a list
                        hs_code = response["data"][0]["hs_code"]
                        if isinstance(hs_code, str):
                            st.session_state.hs_codes = [hs_code]
                        elif isinstance(hs_code, list):
                            st.session_state.hs_codes = hs_code
                        else:
                            st.session_state.hs_codes = [str(hs_code)]

                        st.session_state.product_family = response["data"][0]["product_family"]
                        st.session_state.step = 2
                        st.rerun()
                    else:
                        error_msg = response.get("message", "No HS codes found for the given chemical.")
                        st.error(f"{error_msg} Please try different parameters.")
            else:
                st.warning("Please enter a chemical name.")

    # Step 2: Select HS code
    elif st.session_state.step == 2:
        st.markdown("<div class='card'>", unsafe_allow_html=True)
        st.subheader("Step 2: Select HS Code")

        # Create list of HS codes (just the code part)
        hs_code_list = []
        hs_code_map = {}

        for code in st.session_state.hs_codes:
            if isinstance(code, str):
                # Handle string HS codes
                hs_code_raw = code.strip()
                if ' ' in hs_code_raw or ',' in hs_code_raw:
                    hs_code_clean = hs_code_raw.replace(',', ' ').split()[0]
                else:
                    hs_code_clean = hs_code_raw
                hs_code_list.append(hs_code_clean)
                hs_code_map[hs_code_clean] = hs_code_clean
            elif isinstance(code, dict) and 'hs_code' in code and code['hs_code']:
                # Handle dict HS codes (legacy support)
                hs_code_raw = code['hs_code'].strip()
                if ' ' in hs_code_raw or ',' in hs_code_raw:
                    hs_code_clean = hs_code_raw.replace(',', ' ').split()[0]
                else:
                    hs_code_clean = hs_code_raw
                hs_code_list.append(hs_code_clean)
                hs_code_map[hs_code_clean] = hs_code_clean

        # Add placeholder for manual entry
        default_text = "Select or type an HS code"

        # Use st.selectbox with a free-text fallback
        selected_hs_code = st.selectbox(
            "Select or manually enter an HS code:",
            options=[default_text] + hs_code_list,
            index=0
        )

        # If user selects default, show text_input to enter manually
        if selected_hs_code == default_text:
            manual_entry = st.text_input("Enter HS code manually:")
            if manual_entry:
                selected_hs_code = manual_entry.strip()

        # Optional: validate or display selected code
        if selected_hs_code and selected_hs_code != default_text:
            st.success(f"Using HS Code: {selected_hs_code}")

        # Add placeholder for manual entry
        default_text_pf = "Select or type an Product Family"

        # Use st.selectbox with a free-text fallback
        selected_productFamily = st.selectbox(
            "Select or manually Product Family:",
            options=[default_text_pf] + st.session_state.product_family,
            index=0
        )

        # If user selects default, show text_input to enter manually
        if selected_productFamily == default_text_pf:
            manual_entry_pf = st.text_input("Enter Product Family manually:")
            if manual_entry_pf:
                selected_productFamily = manual_entry_pf.strip()
                st.session_state.selected_productFamily=selected_productFamily

        # Optional: validate or display selected code
        if selected_productFamily and selected_productFamily != default_text:
            st.success(f"Using Product Family: {selected_productFamily}")
            st.session_state.selected_productFamily=selected_productFamily

        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("← Back", use_container_width=True):
                st.session_state.step = 1
                st.rerun()

        with col2:
            if st.button("Find Top Exporting Countries", type="primary", use_container_width=True):
                # Store the selected HS code in session state
                st.session_state.selected_hs_code = selected_hs_code

                # Prepare chemical name for API call
                temp_chemical = st.session_state.selected_productFamily
                st.write(temp_chemical)

                # Call appropriate API based on tariff setting
                if st.session_state.tariff == 0:
                    # Call API to get top countries without tariff info
                    response = api_get_top_countries(
                        selected_hs_code,
                        temp_chemical,
                        st.session_state.destination
                    )
                else:
                    # Call API to get top countries with tariff info
                    response = api_get_top_countries_tariff(
                        selected_hs_code,
                        temp_chemical,
                        st.session_state.destination
                    )

                # Check if API returned valid data
                if response["status"] == "success" and response["data"]:
                    # Use the API data by default
                    st.session_state.countries_data = response["data"]
                    st.session_state.using_llm_data = False

                    # Check if all countries have missing or "Not found" data
                    all_data_missing = True
                    for country_data in response["data"]:
                        # Check if transaction_count, export_volume_tons, or avg_price_per_ton are all missing or zero
                        if (country_data.get("transaction_count", 0) > 0 and
                            country_data.get("export_volume_tons", 0) > 0 and
                            country_data.get("avg_price_per_ton", 0) > 0 and
                            country_data.get("country", "") != "Not found" and
                            country_data.get("country", "") != "Unknown"):
                            # At least one country has valid data
                            all_data_missing = False
                            break

                    # Always generate LLM data for comparison
                    # Generate LLM data
                    llm_response = generate_llm_data(
                        selected_hs_code,
                        st.session_state.chemical_name,
                        st.session_state.destination,
                        is_tariff=(st.session_state.tariff == 1)
                    )

                    # Store the LLM data in session state
                    st.session_state.llm_countries_data = llm_response["data"]

                    # Move to the next step
                    st.session_state.step = 3
                    st.rerun()
                else:
                    # Generate LLM data
                    llm_response = generate_llm_data(
                        selected_hs_code,
                        st.session_state.chemical_name,
                        st.session_state.destination,
                        is_tariff=(st.session_state.tariff == 1)
                    )

                    # Store the LLM data in session state
                    st.session_state.llm_countries_data = llm_response["data"]

                    # If no database data, create empty list
                    st.session_state.countries_data = []

                    # Move to the next step
                    st.session_state.step = 3
                    st.rerun()
        st.markdown("</div>", unsafe_allow_html=True)

    # Step 3: View and select top exporting countries
    elif st.session_state.step == 3:
        st.markdown("<div class='card'>", unsafe_allow_html=True)
        st.subheader("Step 3: Top Exporting Countries")
        st.markdown(f"HS Code: **{st.session_state.selected_hs_code}**")

        # Always fetch LLM data if not already available
        if st.session_state.llm_countries_data is None:
            with st.spinner("Generating LLM data for comparison..."):
                llm_response = generate_llm_data(
                    st.session_state.selected_hs_code,
                    st.session_state.chemical_name,
                    st.session_state.destination,
                    is_tariff=(st.session_state.tariff == 1)
                )
                st.session_state.llm_countries_data = llm_response["data"]

        # Filter options
        col1, col2 = st.columns(2)
        with col1:
            months = st.slider("Months of Data", min_value=1, max_value=24, value=12,
                            help="Select the number of months of historical data to analyze")
        with col2:
            if st.button("Refresh Data", use_container_width=True):
                #Call API to get updated top countries
                with st.spinner("Fetching country data..."):
                    # Prepare chemical name for API call
                    temp_chemical = st.session_state.selected_productFamily
                    st.write(temp_chemical)

                    # Call appropriate API based on tariff setting
                    if st.session_state.tariff == 0:
                        # Call API to get top countries without tariff info
                        response = api_get_top_countries(
                            st.session_state.selected_hs_code,
                            temp_chemical,
                            st.session_state.destination,
                            months
                        )
                    else:
                        # Call API to get top countries with tariff info
                        response = api_get_top_countries_tariff(
                            st.session_state.selected_hs_code,
                            temp_chemical,
                            st.session_state.destination,
                            months
                        )

                    # Check if API returned valid data
                    if response["status"] == "success" and response["data"]:
                        # Use the API data by default
                        st.session_state.countries_data = response["data"]
                        st.session_state.using_llm_data = False

                        # Generate LLM data
                        llm_response = generate_llm_data(
                            st.session_state.selected_hs_code,
                            st.session_state.chemical_name,
                            st.session_state.destination,
                            is_tariff=(st.session_state.tariff == 1)
                        )

                        # Store the LLM data in session state
                        st.session_state.llm_countries_data = llm_response["data"]

                        # Refresh the page
                        st.rerun()
                    elif response["status"] == "warning":
                        # If API returned a warning, show warning
                        st.warning(f"API Warning: {response.get('message', 'No data found')}. Empty database data will be shown.")

                        # Generate LLM data
                        llm_response = generate_llm_data(
                            st.session_state.selected_hs_code,
                            st.session_state.chemical_name,
                            st.session_state.destination,
                            is_tariff=(st.session_state.tariff == 1)
                        )

                        # Store the LLM data in session state
                        st.session_state.llm_countries_data = llm_response["data"]

                        # If no database data, create empty list
                        st.session_state.countries_data = []

                        # Refresh the page
                        st.rerun()
                    else:
                        # If API returned an error, show error
                        st.error(f"API Error: {response.get('message', 'Unknown error')}. Empty database data will be shown.")

                        # Generate LLM data
                        llm_response = generate_llm_data(
                            st.session_state.selected_hs_code,
                            st.session_state.chemical_name,
                            st.session_state.destination,
                            is_tariff=(st.session_state.tariff == 1)
                        )

                        # Store the LLM data in session state
                        st.session_state.llm_countries_data = llm_response["data"]

                        # If no database data, create empty list
                        st.session_state.countries_data = []

                        # Refresh the page
                        st.rerun()

        # Display database data section
        st.markdown("## Data from Database")

        if not st.session_state.countries_data or len(st.session_state.countries_data) == 0:
            st.info("No database data available. Try adjusting your filters or selecting a different HS code.")
        else:
            # Display country map visualization
            if len(st.session_state.countries_data) > 1:
                try:
                    st.plotly_chart(create_country_map_chart(st.session_state.countries_data), use_container_width=True)
                except Exception as e:
                    st.error(f"Error creating country map: {str(e)}")

            # Display country comparison chart
            if len(st.session_state.countries_data) > 1:
                try:
                    st.plotly_chart(create_country_comparison_chart(st.session_state.countries_data), use_container_width=True)
                except Exception as e:
                    st.error(f"Error creating country chart: {str(e)}")

            # Display country data in cards
            st.markdown("### Select a Country")

            # Loop through each country in the database data
            for country_data in st.session_state.countries_data:
                # Create a container for each country
                country_container = st.container()

                # Display within the container
                with country_container:

                    # Different layouts based on tariff setting
                    if st.session_state.tariff == 0:
                        # Non-tariff display (5 columns)
                        cols = st.columns([2, 2, 2, 2, 2])

                        # Column 1: Country name with database icon
                        with cols[0]:
                            st.markdown(f"{country_data['country']}")

                        # Column 2: Transaction count
                        with cols[1]:
                            st.markdown(f"Transactions: {format_number(country_data['transaction_count'])}")

                        # Column 3: Export volume
                        with cols[2]:
                            unit_display = country_data.get('unit', 'tons')
                            st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")

                        # Column 4: Average price
                        with cols[3]:
                            unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                            st.markdown(f"Avg Price: {unit_price}")

                        # Column 5: Select button
                        with cols[4]:
                            if st.button(f"Select", key=f"country_{country_data['country']}"):
                                # Store selected country
                                st.session_state.selected_country = country_data['country']

                                # Get supplier data from both sources
                                with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                                    # Get database suppliers
                                    db_response = api_get_top_suppliers(
                                        st.session_state.selected_hs_code,
                                        country_data['country']
                                    )

                                    # Store database suppliers
                                    if db_response["status"] == "success" and db_response["data"]:
                                        st.session_state.db_suppliers_data = db_response["data"]
                                    else:
                                        # Empty list if no database data
                                        st.session_state.db_suppliers_data = []

                                    # Get LLM suppliers
                                    with st.spinner(f"Finding real suppliers in {country_data['country']} for {st.session_state.chemical_name}..."):
                                        # Call the function to get real supplier data
                                        llm_response = get_real_suppliers_with_llm(
                                            st.session_state.chemical_name,
                                            st.session_state.selected_hs_code,
                                            country_data['country']
                                        )

                                        if llm_response["status"] == "success":
                                            st.session_state.llm_suppliers_data = llm_response["data"]
                                        else:
                                            # If failed to get real suppliers, fall back to generic data
                                            st.warning(f"Could not find real suppliers: {llm_response['message']}. Using generic supplier data instead.")
                                            st.session_state.llm_suppliers_data = generate_generic_suppliers(country_data['country'], 5)

                                    # Combine both data sources for backward compatibility
                                    st.session_state.suppliers_data = st.session_state.db_suppliers_data + st.session_state.llm_suppliers_data

                                    # Move to Step 4
                                    st.session_state.step = 4
                                    st.rerun()
                    else:
                        # Tariff display (9 columns)
                        cols = st.columns([1, 1.5, 1.5, 2, 1.5, 1, 2, 2, 1.5])

                        # Column 1: Country name with database icon
                        with cols[0]:
                            st.markdown(f"{country_data['country']}")

                        # Column 2: Records count
                        with cols[1]:
                            st.markdown(f"#Records: {format_number(country_data['transaction_count'])}")

                        # Column 3: Export volume
                        with cols[2]:
                            unit_display = country_data.get('unit', 'tons')
                            st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")

                        # Column 4: Average price
                        with cols[3]:
                            unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                            st.markdown(f"Avg Price: {unit_price}")

                        # Column 5: Duty
                        with cols[4]:
                            duty = f"${country_data.get('duty', 'N/A')}"
                            st.markdown(f"Duty: {duty}")

                        # Column 6: Additional duty
                        with cols[5]:
                            footer_duty = f"${country_data.get('footer_duty', 'N/A')}"
                            st.markdown(f"Addtl Duty: {footer_duty}")

                        # Column 7: In-place tariff
                        with cols[6]:
                            inplace_Trump = f"${country_data.get('new_tariff', 'N/A')}"
                            st.markdown(f"Tariff-InPlace: {inplace_Trump}")

                        # Column 8: Proposed tariff
                        with cols[7]:
                            proposed_Trump = f"${country_data.get('proposed_tariff', 'N/A')}"
                            st.markdown(f"Tariff-Proposed: {proposed_Trump}")

                        # Column 9: Select button
                        with cols[8]:
                            if st.button(f"Select", key=f"country_{country_data['country']}"):
                                # Store selected country
                                st.session_state.selected_country = country_data['country']

                                # Get supplier data from both sources
                                with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                                    # Get database suppliers
                                    db_response = api_get_top_suppliers(
                                        st.session_state.selected_hs_code,
                                        country_data['country']
                                    )

                                    # Store database suppliers
                                    if db_response["status"] == "success" and db_response["data"]:
                                        st.session_state.db_suppliers_data = db_response["data"]
                                    else:
                                        # Empty list if no database data
                                        st.session_state.db_suppliers_data = []

                                    # Get LLM suppliers
                                    with st.spinner(f"Finding real suppliers in {country_data['country']} for {st.session_state.chemical_name}..."):
                                        # Call the function to get real supplier data
                                        llm_response = get_real_suppliers_with_llm(
                                            st.session_state.chemical_name,
                                            st.session_state.selected_hs_code,
                                            country_data['country']
                                        )

                                        if llm_response["status"] == "success":
                                            st.session_state.llm_suppliers_data = llm_response["data"]
                                        else:
                                            # If failed to get real suppliers, fall back to generic data
                                            st.warning(f"Could not find real suppliers: {llm_response['message']}. Using generic supplier data instead.")
                                            st.session_state.llm_suppliers_data = generate_generic_suppliers(country_data['country'], 5)

                                    # Combine both data sources for backward compatibility
                                    st.session_state.suppliers_data = st.session_state.db_suppliers_data + st.session_state.llm_suppliers_data

                                    # Move to Step 4
                                    st.session_state.step = 4
                                    st.rerun()

        # Display LLM data section
        st.markdown("## Data from WEB")

        if not st.session_state.llm_countries_data or len(st.session_state.llm_countries_data) == 0:
            st.info("No LLM data available.")
        else:
            # Display country map visualization
            if len(st.session_state.llm_countries_data) > 1:
                try:
                    st.plotly_chart(create_country_map_chart(st.session_state.llm_countries_data), use_container_width=True)
                except Exception as e:
                    st.error(f"Error creating country map: {str(e)}")

            # Display country comparison chart
            if len(st.session_state.llm_countries_data) > 1:
                try:
                    st.plotly_chart(create_country_comparison_chart(st.session_state.llm_countries_data), use_container_width=True)
                except Exception as e:
                    st.error(f"Error creating country chart: {str(e)}")

            # Display country data in cards
            st.markdown("### Select a Country")

            # Loop through each country in the LLM data
            for country_data in st.session_state.llm_countries_data:
                # Create a container for each country
                country_container = st.container()

                # Display within the container
                with country_container:
                    # Different layouts based on tariff setting
                    if st.session_state.tariff == 0:
                        # Non-tariff display (5 columns)
                        cols = st.columns([2, 2, 2, 2, 2])

                        # Column 1: Country name with source indicator
                        with cols[0]:
                            st.markdown(f"{country_data['country']}")

                        # Column 2: Transaction count
                        with cols[1]:
                            st.markdown(f"Transactions: {format_number(country_data['transaction_count'])}")

                        # Column 3: Export volume
                        with cols[2]:
                            unit_display = country_data.get('unit', 'tons')
                            st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")

                        # Column 4: Average price
                        with cols[3]:
                            unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                            st.markdown(f"Avg Price: {unit_price}")

                        # Column 5: Select button
                        with cols[4]:
                            if st.button(f"Select", key=f"llm_country_{country_data['country']}"):
                                # Store selected country
                                st.session_state.selected_country = country_data['country']

                                # Get supplier data from both sources
                                with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                                    # Get database suppliers
                                    db_response = api_get_top_suppliers(
                                        st.session_state.selected_hs_code,
                                        country_data['country']
                                    )

                                    # Store database suppliers
                                    if db_response["status"] == "success" and db_response["data"]:
                                        st.session_state.db_suppliers_data = db_response["data"]
                                    else:
                                        # Empty list if no database data
                                        st.session_state.db_suppliers_data = []

                                    # Get LLM suppliers
                                    with st.spinner(f"Finding real suppliers in {country_data['country']} for {st.session_state.chemical_name}..."):
                                        # Call the function to get real supplier data
                                        llm_response = get_real_suppliers_with_llm(
                                            st.session_state.chemical_name,
                                            st.session_state.selected_hs_code,
                                            country_data['country']
                                        )

                                        if llm_response["status"] == "success":
                                            st.session_state.llm_suppliers_data = llm_response["data"]
                                        else:
                                            # If failed to get real suppliers, fall back to generic data
                                            st.warning(f"Could not find real suppliers: {llm_response['message']}. Using generic supplier data instead.")
                                            st.session_state.llm_suppliers_data = generate_generic_suppliers(country_data['country'], 5)

                                    # Combine both data sources for backward compatibility
                                    st.session_state.suppliers_data = st.session_state.db_suppliers_data + st.session_state.llm_suppliers_data

                                    # Move to Step 4
                                    st.session_state.step = 4
                                    st.rerun()
                    else:
                        # Tariff display (9 columns)
                        cols = st.columns([1, 1.5, 1.5, 2, 1.5, 1, 2, 2, 1.5])

                        # Column 1: Country name with source indicator
                        with cols[0]:
                            st.markdown(f"{country_data['country']}")

                        # Column 2: Records count
                        with cols[1]:
                            st.markdown(f"#Records: {format_number(country_data['transaction_count'])}")

                        # Column 3: Export volume
                        with cols[2]:
                            unit_display = country_data.get('unit', 'tons')
                            st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")

                        # Column 4: Average price
                        with cols[3]:
                            unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                            st.markdown(f"Avg Price: {unit_price}")

                        # Column 5: Duty
                        with cols[4]:
                            duty = f"${country_data.get('duty', 'N/A')}"
                            st.markdown(f"Duty: {duty}")

                        # Column 6: Additional duty
                        with cols[5]:
                            footer_duty = f"${country_data.get('footer_duty', 'N/A')}"
                            st.markdown(f"Addtl Duty: {footer_duty}")

                        # Column 7: In-place tariff
                        with cols[6]:
                            inplace_Trump = f"${country_data.get('new_tariff', 'N/A')}"
                            st.markdown(f"Tariff-InPlace: {inplace_Trump}")

                        # Column 8: Proposed tariff
                        with cols[7]:
                            proposed_Trump = f"${country_data.get('proposed_tariff', 'N/A')}"
                            st.markdown(f"Tariff-Proposed: {proposed_Trump}")

                        # Column 9: Select button
                        with cols[8]:
                            if st.button(f"Select", key=f"llm_country_{country_data['country']}"):
                                # Store selected country
                                st.session_state.selected_country = country_data['country']

                                # Get supplier data from both sources
                                with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                                    # Get database suppliers
                                    db_response = api_get_top_suppliers(
                                        st.session_state.selected_hs_code,
                                        country_data['country']
                                    )

                                    # Store database suppliers
                                    if db_response["status"] == "success" and db_response["data"]:
                                        st.session_state.db_suppliers_data = db_response["data"]
                                    else:
                                        # Empty list if no database data
                                        st.session_state.db_suppliers_data = []

                                    # Get LLM suppliers
                                    with st.spinner(f"Finding real suppliers in {country_data['country']} for {st.session_state.chemical_name}..."):
                                        # Call the function to get real supplier data
                                        llm_response = get_real_suppliers_with_llm(
                                            st.session_state.chemical_name,
                                            st.session_state.selected_hs_code,
                                            country_data['country']
                                        )

                                        if llm_response["status"] == "success":
                                            st.session_state.llm_suppliers_data = llm_response["data"]
                                        else:
                                            # If failed to get real suppliers, fall back to generic data
                                            st.warning(f"Could not find real suppliers: {llm_response['message']}. Using generic supplier data instead.")
                                            st.session_state.llm_suppliers_data = generate_generic_suppliers(country_data['country'], 5)

                                    # Combine both data sources for backward compatibility
                                    st.session_state.suppliers_data = st.session_state.db_suppliers_data + st.session_state.llm_suppliers_data

                                    # Move to Step 4
                                    st.session_state.step = 4
                                    st.rerun()

        if st.button("← Back", use_container_width=True):
            st.session_state.step = 2
            st.rerun()
        st.markdown("</div>", unsafe_allow_html=True)

    # Step 4: View and select suppliers
    elif st.session_state.step == 4:
        st.markdown("<div class='card'>", unsafe_allow_html=True)
        st.subheader("Step 4: Suppliers")
        st.markdown(f"HS Code: **{st.session_state.selected_hs_code}** | Country: **{st.session_state.selected_country}**")

        # If supplier details are being shown, display them
        if "show_supplier_details" in st.session_state and st.session_state.show_supplier_details:
            st.markdown("<div class='supplier-details-container'>", unsafe_allow_html=True)
            st.markdown(f"<div class='supplier-details-header'>Supplier Details: {st.session_state.selected_supplier}</div>", unsafe_allow_html=True)

            supplier_details = st.session_state.supplier_details

            # Overview section
            st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
            st.markdown("<div class='supplier-section-title'>Overview</div>", unsafe_allow_html=True)
            st.markdown(supplier_details.get('overview', 'No overview available'))
            st.markdown("</div>", unsafe_allow_html=True)

            # Products section
            if 'products' in supplier_details and supplier_details['products']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Products</div>", unsafe_allow_html=True)
                st.markdown(supplier_details.get('products', 'No product information available'))
                st.markdown("</div>", unsafe_allow_html=True)

            # Manufacturing capabilities section
            if 'manufacturing' in supplier_details and supplier_details['manufacturing']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Manufacturing Capabilities</div>", unsafe_allow_html=True)
                st.markdown(supplier_details.get('manufacturing', 'No manufacturing information available'))
                st.markdown("</div>", unsafe_allow_html=True)

            # Export markets section
            if 'export_markets' in supplier_details and supplier_details['export_markets']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Export Markets</div>", unsafe_allow_html=True)
                st.markdown(supplier_details.get('export_markets', 'No export market information available'))
                st.markdown("</div>", unsafe_allow_html=True)

            # Contact information section
            if 'contact' in supplier_details and supplier_details['contact']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Contact Information</div>", unsafe_allow_html=True)

                contact = supplier_details['contact']
                contact_cols = st.columns(2)

                with contact_cols[0]:
                    st.markdown(f"**Email:** {contact.get('email', 'Not available')}")
                    st.markdown(f"**Phone:** {contact.get('phone', 'Not available')}")

                with contact_cols[1]:
                    st.markdown(f"**Address:** {contact.get('address', 'Not available')}")
                    if 'website' in contact and contact['website'] != 'Not available':
                        st.markdown(f"**Website:** [{contact['website']}]({contact['website']})")
                    else:
                        st.markdown("**Website:** Not available")

                st.markdown("</div>", unsafe_allow_html=True)

            # Certifications section
            if 'certifications' in supplier_details and supplier_details['certifications']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Certifications</div>", unsafe_allow_html=True)

                certifications = supplier_details['certifications']

                # If it's a string, convert to list
                if isinstance(certifications, str):
                    certifications = [cert.strip() for cert in certifications.split(',')]

                # Create a clean grid layout for certifications
                num_cols = 2 if len(certifications) > 3 else 1
                cert_cols = st.columns(num_cols)

                for i, cert in enumerate(certifications):
                    with cert_cols[i % num_cols]:
                        st.markdown(f"✓ {cert}")

                st.markdown("</div>", unsafe_allow_html=True)

            # Advantages section
            if 'advantages' in supplier_details and supplier_details['advantages']:
                st.markdown("<div class='supplier-section'>", unsafe_allow_html=True)
                st.markdown("<div class='supplier-section-title'>Competitive Advantages</div>", unsafe_allow_html=True)

                advantages = supplier_details['advantages']

                # If it's a string, convert to list
                if isinstance(advantages, str):
                    import re
                    advantages = re.split(r'[,;\n]', advantages)
                    advantages = [adv.strip() for adv in advantages if adv.strip()]

                # Display as bullet points
                for adv in advantages:
                    st.markdown(f"- {adv}")

                st.markdown("</div>", unsafe_allow_html=True)

            # Close button
            if st.button("Close Details", use_container_width=True):
                st.session_state.show_supplier_details = False
                st.session_state.selected_supplier = None
                st.session_state.supplier_details = None
                st.rerun()

            st.markdown("</div>", unsafe_allow_html=True)
        else:
            # Display database suppliers section
            st.markdown("## Suppliers from Database")

            if not st.session_state.db_suppliers_data or len(st.session_state.db_suppliers_data) == 0:
                st.info("No suppliers found in database for this country and HS code.")
            else:
                # Create a visualization of suppliers
                if len(st.session_state.db_suppliers_data) > 1:
                    try:
                        st.plotly_chart(create_supplier_comparison_chart(st.session_state.db_suppliers_data), use_container_width=True)
                    except Exception as e:
                        st.error(f"Error creating chart: {str(e)}")

                # Display each supplier in a card with a select button
                for i, supplier in enumerate(st.session_state.db_suppliers_data):
                    cols = st.columns([3, 2, 2, 2, 1])

                    with cols[0]:
                        # Check if supplier name is missing or just a dash
                        if supplier['name'] == "-" or not supplier['name'] or supplier['name'].strip() == "":
                            st.markdown("**Supplier name is available on select**")
                        else:
                            st.markdown(f"**{supplier['name']}**")

                    with cols[1]:
                        st.markdown(f"Transactions: {format_number(supplier['transaction_count'])}")

                    with cols[2]:
                        unit_display = supplier.get('unit', 'tons')
                        st.markdown(f"Volume: {format_number(supplier['export_volume_tons'])} {unit_display}")

                    with cols[3]:
                        unit_price = f"${format_number(supplier['avg_price_per_ton'])}/{unit_display}"
                        st.markdown(f"Avg Price: {unit_price}")

                    with cols[4]:
                        if st.button("Select", key=f"db_supplier_{i}"):
                            # Store selected supplier
                            display_name = supplier['name']
                            if display_name == "-" or not display_name or display_name.strip() == "":
                                display_name = f"Supplier from {st.session_state.selected_country}"

                            st.session_state.selected_supplier = display_name

                            # Generate supplier details with LLM
                            with st.spinner(f"Generating details for {display_name}..."):
                                # Prepare the prompt for Perplexity
                                prompt = f"""Generate a detailed profile for {display_name}, a chemical supplier from {st.session_state.selected_country} that exports {st.session_state.chemical_name} (HS code: {st.session_state.selected_hs_code}).

                                Include the following sections:
                                1. Company overview (history, size, market position)
                                2. Products (focus on {st.session_state.chemical_name} and related chemicals)
                                3. Manufacturing capabilities
                                4. Export markets
                                5. Certifications (ISO, industry-specific)
                                6. Competitive advantages
                                7. Contact information (website, email, phone, address)

                                Format the response as a JSON object with the following structure:
                                {{
                                    "overview": "Detailed company overview...",
                                    "products": "Products information...",
                                    "manufacturing": "Manufacturing capabilities...",
                                    "export_markets": "Export markets information...",
                                    "certifications": ["ISO 9001", "ISO 14001", ...],
                                    "advantages": ["Advantage 1", "Advantage 2", ...],
                                    "contact": {{
                                        "website": "company website",
                                        "email": "contact email",
                                        "phone": "phone number",
                                        "address": "full address"
                                    }}
                                }}

                                Return ONLY the JSON object without any explanation or additional text.
                                """

                                # Check if we have a Perplexity API key
                                api_key = ""
                                try:
                                    if hasattr(st.session_state, 'perplexity_api_key') and st.session_state.perplexity_api_key:
                                        api_key = st.session_state.perplexity_api_key
                                except Exception as e:
                                    st.error(f"Error accessing API key: {str(e)}")

                                if api_key:
                                    try:
                                        # Make API request to Perplexity
                                        headers = {
                                            "Authorization": f"Bearer {api_key}",
                                            "Content-Type": "application/json"
                                        }

                                        data = {
                                            "model": "sonar",
                                            "messages": [
                                                {
                                                    "role": "system",
                                                    "content": "You are a helpful assistant that provides accurate information about chemical suppliers. Your primary task is to find real, accurate information about chemical companies by searching the web."
                                                },
                                                {
                                                    "role": "user",
                                                    "content": prompt
                                                }
                                            ]
                                        }

                                        response = requests.post(
                                            "https://api.perplexity.ai/chat/completions",
                                            headers=headers,
                                            json=data
                                        )

                                        if response.status_code == 200:
                                            # Parse the response
                                            response_data = response.json()
                                            llm_response = response_data["choices"][0]["message"]["content"]

                                            # Try to extract JSON from the response
                                            try:
                                                # Find JSON object in the response
                                                import json
                                                import re

                                                # Try to extract JSON from the response
                                                json_match = re.search(r'({[\s\S]*})', llm_response)
                                                if json_match:
                                                    json_str = json_match.group(1)
                                                    supplier_details = json.loads(json_str)
                                                else:
                                                    # If no JSON found, try parsing the whole response
                                                    supplier_details = json.loads(llm_response)

                                                # Store the details in session state
                                                st.session_state.supplier_details = supplier_details
                                                st.session_state.show_supplier_details = True
                                                st.rerun()
                                            except json.JSONDecodeError:
                                                # If JSON parsing fails, create a generic profile
                                                st.error("Failed to parse supplier details. Creating generic profile.")
                                                supplier_details = {
                                                    "overview": f"{display_name} is a chemical supplier based in {st.session_state.selected_country} that specializes in {st.session_state.chemical_name} and related chemicals.",
                                                    "contact": {
                                                        "website": "Not available",
                                                        "email": "Not available",
                                                        "phone": "Not available",
                                                        "address": f"{st.session_state.selected_country}"
                                                    }
                                                }
                                                st.session_state.supplier_details = supplier_details
                                                st.session_state.show_supplier_details = True
                                                st.rerun()
                                        else:
                                            st.error(f"API request failed: {response.status_code}")
                                    except Exception as e:
                                        st.error(f"Error generating supplier details: {str(e)}")
                                else:
                                    st.error("Perplexity API key not found. Please set the PERPLEXITY_API_KEY environment variable.")

                    st.markdown("---")

            # Display LLM suppliers section
            st.markdown("## Suppliers from WEB")

            if not st.session_state.llm_suppliers_data or len(st.session_state.llm_suppliers_data) == 0:
                st.info("No suppliers found from WEB for this country and HS code.")
            else:
                # Create a visualization of suppliers
                if len(st.session_state.llm_suppliers_data) > 1:
                    try:
                        st.plotly_chart(create_supplier_comparison_chart(st.session_state.llm_suppliers_data), use_container_width=True)
                    except Exception as e:
                        st.error(f"Error creating chart: {str(e)}")

                # Display each supplier in a card with a select button
                for i, supplier in enumerate(st.session_state.llm_suppliers_data):
                    cols = st.columns([3, 2, 2, 2, 1])

                    with cols[0]:
                        # Check if supplier name is missing or just a dash
                        if supplier['name'] == "-" or not supplier['name'] or supplier['name'].strip() == "":
                            st.markdown("**Supplier name is available on select**")
                        else:
                            st.markdown(f"**{supplier['name']}**")

                    with cols[1]:
                        st.markdown(f"Transactions: {format_number(supplier['transaction_count'])}")

                    with cols[2]:
                        unit_display = supplier.get('unit', 'tons')
                        st.markdown(f"Volume: {format_number(supplier['export_volume_tons'])} {unit_display}")

                    with cols[3]:
                        unit_price = f"${format_number(supplier['avg_price_per_ton'])}/{unit_display}"
                        st.markdown(f"Avg Price: {unit_price}")

                    with cols[4]:
                        if st.button("Select", key=f"llm_supplier_{i}"):
                            # Store selected supplier
                            display_name = supplier['name']
                            if display_name == "-" or not display_name or display_name.strip() == "":
                                display_name = f"Supplier from {st.session_state.selected_country}"

                            st.session_state.selected_supplier = display_name

                            # Generate supplier details with LLM
                            with st.spinner(f"Generating details for {display_name}..."):
                                # Prepare the prompt for Perplexity
                                prompt = f"""Generate a detailed profile for {display_name}, a chemical supplier from {st.session_state.selected_country} that exports {st.session_state.chemical_name} (HS code: {st.session_state.selected_hs_code}).

                                Include the following sections:
                                1. Company overview (history, size, market position)
                                2. Products (focus on {st.session_state.chemical_name} and related chemicals)
                                3. Manufacturing capabilities
                                4. Export markets
                                5. Certifications (ISO, industry-specific)
                                6. Competitive advantages
                                7. Contact information (website, email, phone, address)

                                Format the response as a JSON object with the following structure:
                                {{
                                    "overview": "Detailed company overview...",
                                    "products": "Products information...",
                                    "manufacturing": "Manufacturing capabilities...",
                                    "export_markets": "Export markets information...",
                                    "certifications": ["ISO 9001", "ISO 14001", ...],
                                    "advantages": ["Advantage 1", "Advantage 2", ...],
                                    "contact": {{
                                        "website": "company website",
                                        "email": "contact email",
                                        "phone": "phone number",
                                        "address": "full address"
                                    }}
                                }}

                                Return ONLY the JSON object without any explanation or additional text.
                                """

                                # Check if we have a Perplexity API key
                                api_key = ""
                                try:
                                    if hasattr(st.session_state, 'perplexity_api_key') and st.session_state.perplexity_api_key:
                                        api_key = st.session_state.perplexity_api_key
                                except Exception as e:
                                    st.error(f"Error accessing API key: {str(e)}")

                                if api_key:
                                    try:
                                        # Make API request to Perplexity
                                        headers = {
                                            "Authorization": f"Bearer {api_key}",
                                            "Content-Type": "application/json"
                                        }

                                        data = {
                                            "model": "sonar",
                                            "messages": [
                                                {
                                                    "role": "system",
                                                    "content": "You are a helpful assistant that provides accurate information about chemical suppliers. Your primary task is to find real, accurate information about chemical companies by searching the web."
                                                },
                                                {
                                                    "role": "user",
                                                    "content": prompt
                                                }
                                            ]
                                        }

                                        response = requests.post(
                                            "https://api.perplexity.ai/chat/completions",
                                            headers=headers,
                                            json=data
                                        )

                                        if response.status_code == 200:
                                            # Parse the response
                                            response_data = response.json()
                                            llm_response = response_data["choices"][0]["message"]["content"]

                                            # Try to extract JSON from the response
                                            try:
                                                # Find JSON object in the response
                                                import json
                                                import re

                                                # Try to extract JSON from the response
                                                json_match = re.search(r'({[\s\S]*})', llm_response)
                                                if json_match:
                                                    json_str = json_match.group(1)
                                                    supplier_details = json.loads(json_str)
                                                else:
                                                    # If no JSON found, try parsing the whole response
                                                    supplier_details = json.loads(llm_response)

                                                # Store the details in session state
                                                st.session_state.supplier_details = supplier_details
                                                st.session_state.show_supplier_details = True
                                                st.rerun()
                                            except json.JSONDecodeError:
                                                # If JSON parsing fails, create a generic profile
                                                st.error("Failed to parse supplier details. Creating generic profile.")
                                                supplier_details = {
                                                    "overview": f"{display_name} is a chemical supplier based in {st.session_state.selected_country} that specializes in {st.session_state.chemical_name} and related chemicals.",
                                                    "contact": {
                                                        "website": "Not available",
                                                        "email": "Not available",
                                                        "phone": "Not available",
                                                        "address": f"{st.session_state.selected_country}"
                                                    }
                                                }
                                                st.session_state.supplier_details = supplier_details
                                                st.session_state.show_supplier_details = True
                                                st.rerun()
                                        else:
                                            st.error(f"API request failed: {response.status_code}")
                                    except Exception as e:
                                        st.error(f"Error generating supplier details: {str(e)}")
                                else:
                                    st.error("Perplexity API key not found. Please set the PERPLEXITY_API_KEY environment variable.")

                    st.markdown("---")

            # Navigation buttons
            col1, col2 = st.columns(2)
            with col1:
                if st.button("← Back to Countries", use_container_width=True):
                    st.session_state.step = 3
                    # Clear suppliers data to force refresh on next visit
                    if "suppliers_data" in st.session_state:
                        del st.session_state.suppliers_data
                    if "db_suppliers_data" in st.session_state:
                        del st.session_state.db_suppliers_data
                    if "llm_suppliers_data" in st.session_state:
                        del st.session_state.llm_suppliers_data
                    st.rerun()

            with col2:
                if st.button("Start New Search", type="primary", use_container_width=True):
                    # Reset all session state
                    for key in list(st.session_state.keys()):
                        if key != 'perplexity_api_key':  # Keep API key
                            del st.session_state[key]
                    st.session_state.step = 1
                    st.rerun()

        st.markdown("</div>", unsafe_allow_html=True)

if __name__ == "__main__":
    main()
