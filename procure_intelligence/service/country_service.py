import logging
import re
import requests
import json
from typing import Dict, Any, List, Optional, Tu<PERSON>

from model.country import Country, CountryLookupResult
from service.client.db_client import DatabaseClient
from service.client.llm_client import LLMClient

logger = logging.getLogger(__name__)

class CountryService:
    """
    Service for country-related operations.
    """

    def __init__(self, db_client: DatabaseClient, llm_client: LLMClient = None):
        """
        Initialize the country service.

        Args:
            db_client: Database client for querying trade data
            llm_client: LLM client for generating data when DB data is insufficient
        """
        self.db_client = db_client
        self.llm_client = llm_client
        self.tariff_api_url = "http://*************:5000/tariff"

    def _build_product_desc_condition(self, chemical_name: str, params_list: List) -> Tuple[str, List]:
        """
        Build a SQL condition for product description matching based on chemical name.

        Args:
            chemical_name: Name of the chemical
            params_list: List to append parameters to

        Returns:
            Tuple of SQL condition string and updated params list
        """
        product_desc_condition = ""

        if chemical_name:
            # For now, just use the chemical name directly
            # In a more complete implementation, we would use chemical name variations
            like_conditions = []

            # Add the chemical name as a search term
            like_conditions.append("product_desc ILIKE %s")
            search_term = chemical_name
            if not search_term.startswith('%'):
                search_term = '%' + search_term
            if not search_term.endswith('%'):
                search_term = search_term + '%'
            params_list.append(search_term)

            if like_conditions:
                product_desc_condition = "AND (" + " OR ".join(like_conditions) + ")"

        return product_desc_condition, params_list

    def _build_query_top_countries(self, hs_code: str, destination: str, months: int = 12, chemical_name: str = None) -> Tuple[str, Tuple]:
        """
        Build query to retrieve top geographies supplying products.

        Args:
            hs_code: HS code (will use first 4 digits)
            destination: Destination country
            months: Number of months to look back
            chemical_name: Name of the chemical to search for

        Returns:
            Tuple of SQL query and parameters
        """
        # Extract first 4 digits of HS code
        hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

        # Initialize parameters with required values
        params = [destination, months]

        # Handle product description conditions if chemical_name is provided
        product_desc_condition = ""
        if chemical_name:
            product_desc_condition, params = self._build_product_desc_condition(chemical_name, params)

        # Add hs_code parameter after the product description parameters
        params.append(hs_code_4digit)

        query = f"""
        WITH filtered_by_hs AS (
            SELECT
                parent_coo,
                global_std_unit_id,
                std_qty,
                fob_value_usd,
                date,
                product_desc,
                gross_wt,
                hs_code
            FROM
                volza_trade_data
            WHERE
                parent_cod = %s
                AND date >= CURRENT_DATE - INTERVAL '%s month'
                {product_desc_condition}
                AND SUBSTRING(hs_code, 1, 4) = %s
        ),
        aggregated AS (
            SELECT
                parent_coo AS origin_country,
                global_std_unit_id,
                SUM(std_qty) AS total_quantity,
                AVG(fob_value_usd) AS average_fob,
                MIN(fob_value_usd) AS minimum_fob,
                COUNT(*) AS shipment_count,
                SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
                AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
            FROM
                filtered_by_hs
            WHERE
                CASE
                    -- When gross_wt is a valid number and std_qty exists, use the maximum of both
                    WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
                        GREATEST(gross_wt::INTEGER, std_qty) >= 10
                    -- When only gross_wt is valid, use it
                    WHEN gross_wt ~ '^[0-9]+$' THEN
                        gross_wt::INTEGER >= 10
                    -- When only std_qty exists, use it
                    WHEN std_qty IS NOT NULL THEN
                        std_qty >= 10
                    -- If neither is valid, exclude the row
                    ELSE FALSE
                END
            GROUP BY
                parent_coo, global_std_unit_id
            HAVING
                COUNT(*) >= 1
            ORDER BY
                total_quantity DESC
        ),
        ranked AS (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY origin_country ORDER BY total_quantity DESC) AS rn
            FROM aggregated
        )

        SELECT *
        FROM ranked
        WHERE rn = 1
        ORDER BY total_quantity DESC;
        """

        return query, tuple(params)

    def _fetch_tariff_data(self, country: str, hs_code: str) -> Dict[str, Any]:
        """
        Fetch tariff data from external service for a given country and HS code.

        Args:
            country: Country name
            hs_code: HS code

        Returns:
            Dictionary with tariff data
        """
        logger.info(f"Fetching tariff data for country: {country}, HS code: {hs_code}")

        try:
            # Make the request to the external service
            response = requests.get(
                self.tariff_api_url,
                params={"hs_code": hs_code, "country": country},
                timeout=5
            )

            # Check if the request was successful
            if response.status_code == 200:
                # The response might be a JSON string inside a JSON response
                # First, try to parse as regular JSON
                try:
                    tariff_data = response.json()

                    # If the result is a string, it might be a JSON string that needs further parsing
                    if isinstance(tariff_data, str):
                        tariff_data = json.loads(tariff_data)

                    logger.info(f"Received tariff data for {country}: Total Duty = {tariff_data.get('Total Duty', 'N/A')}")
                    return tariff_data

                except Exception as e:
                    logger.warning(f"JSON parsing error for {country}: {str(e)}")
                    return {"Total Duty": "N/A"}
            else:
                logger.error(f"API request failed for {country} with status code {response.status_code}")
                return {"Total Duty": "N/A"}

        except Exception as e:
            logger.error(f"Error in fetch_tariff_data for {country}: {str(e)}")
            return {"Total Duty": "N/A"}

    def _extract_duty_percentage(self, duty_str: str) -> float:
        """
        Extract numerical percentage from duty string.

        Args:
            duty_str: Duty string (e.g., "6.5%")

        Returns:
            Numerical percentage value
        """
        if not duty_str or duty_str == "NA" or duty_str == "N/A":
            return 0.0

        # Handle the case where duty_str is already a number
        if isinstance(duty_str, (int, float)):
            return float(duty_str)

        # Remove % symbol and convert to float
        try:
            return float(duty_str.replace("%", ""))
        except (ValueError, AttributeError) as e:
            logger.warning(f"Could not parse duty string: {duty_str} - Error: {str(e)}")
            return 0.0

    def get_top_countries(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> CountryLookupResult:
        """
        Get top countries exporting a product with given HS code.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            CountryLookupResult with status and data
        """
        # Format HS code (remove dots if present)
        formatted_hs_code = hs_code.replace(".", "")

        try:
            # Build and execute query
            query, params = self._build_query_top_countries(formatted_hs_code, destination, months, chemical_name)
            result = self.db_client.execute_query(query, params)

            # Transform API data to match expected structure
            transformed_data = []

            for item in result.get("data", []):
                # Handle potential missing or None values
                shipment_count = item.get("shipment_count", 0) or 0
                total_quantity = item.get("total_quantity", 0) or 0
                average_fob = item.get("average_fob", 0) or 0

                # Calculate total value based on average price and total quantity
                total_value = average_fob * total_quantity if average_fob and total_quantity else 0

                # Format the data to match the expected structure
                transformed_item = {
                    "country": item.get("origin_country", "Unknown"),
                    "transaction_count": shipment_count,
                    "export_volume_tons": float(total_quantity) if total_quantity else 0,
                    "avg_price_per_ton": float(average_fob) if average_fob else 0,
                    "total_export_value": total_value,
                    "unit": item.get("global_std_unit_id", "KGS")
                }

                transformed_data.append(transformed_item)

            # If no data was found, try to generate data using LLM
            if not transformed_data and self.llm_client:
                logger.info(f"No data found in database, generating with LLM for {chemical_name}, {hs_code}")

                # Generate data using LLM
                llm_result = self._generate_country_data(hs_code, chemical_name, destination)

                if llm_result["status"] == "success":
                    return CountryLookupResult(
                        status="success",
                        message="Data generated by LLM as fallback mechanism",
                        data=llm_result["data"]
                    )
                else:
                    return CountryLookupResult(
                        status="warning",
                        message="No supplier countries found for the given HS code.",
                        data=[]
                    )

            return CountryLookupResult(
                status="success",
                data=transformed_data
            )

        except Exception as e:
            logger.error(f"Error in get_top_countries: {e}")

            # Try to generate data using LLM as fallback
            if self.llm_client:
                llm_result = self._generate_country_data(hs_code, chemical_name, destination)

                if llm_result["status"] == "success":
                    return CountryLookupResult(
                        status="success",
                        message="Data generated by LLM as fallback mechanism",
                        data=llm_result["data"]
                    )

            return CountryLookupResult(
                status="error",
                message=f"Failed to get top countries: {str(e)}"
            )

    def get_top_countries_with_tariff(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> CountryLookupResult:
        """
        Get top countries exporting a product with given HS code, including tariff information.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            CountryLookupResult with status and data
        """
        # First get the countries without tariff
        country_result = self.get_top_countries(hs_code, chemical_name, destination, months)

        if country_result.status != "success" or not country_result.data:
            return country_result

        # Augment with tariff data for each country
        enriched_countries = []

        for country_data in country_result.data:
            country_name = country_data.get("country")

            # Skip if country name is missing
            if not country_name:
                continue

            # Fetch tariff data
            tariff_data = self._fetch_tariff_data(country_name, hs_code)

            # Extract duty percentage
            duty_str = tariff_data.get("Total Duty", "0%")
            duty_percentage = self._extract_duty_percentage(duty_str)

            # Add duty data to country
            country_data["Total Duty"] = duty_str
            country_data["duty_percentage"] = duty_percentage
            country_data["duty"] = tariff_data.get("duty", "N/A")
            country_data["footer_duty"] = tariff_data.get("footer_duty", "N/A")
            country_data["new_tariff"] = tariff_data.get("New Tariff", "N/A")
            country_data["ad"] = tariff_data.get("Anti-Dumping Duty", "N/A")
            country_data["cvd"] = tariff_data.get("Countervailing Duty", "N/A")
            country_data["proposed_tariff"] = tariff_data.get("Proposed Tariff", "N/A")

            enriched_countries.append(country_data)

        return CountryLookupResult(
            status="success",
            data=enriched_countries
        )

    def _generate_country_data(self, hs_code: str, chemical_name: str, destination: str, is_tariff: bool = False) -> Dict[str, Any]:
        """
        Generate country data using LLM when database data is insufficient.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            is_tariff: Whether to include tariff information

        Returns:
            Dictionary with generated country data
        """
        # Use the LLM client if available
        if self.llm_client:
            logger.info(f"Generating country data using LLM for {chemical_name}, {hs_code}")
            return self.llm_client.generate_country_data(hs_code, chemical_name, destination, is_tariff)

        # Fallback to random data generation if no LLM client
        logger.warning("No LLM client available, generating random country data")
        import random

        # List of common exporting countries for chemicals
        countries = ["China", "Germany", "United States", "Japan", "South Korea",
                    "India", "Netherlands", "Belgium", "France", "United Kingdom",
                    "Italy", "Singapore", "Switzerland", "Spain", "Canada"]

        # Generate 5-8 random countries
        num_countries = random.randint(5, 8)
        selected_countries = random.sample(countries, num_countries)

        # Generate data for each country
        transformed_data = []

        for country in selected_countries:
            # Generate realistic-looking data
            transaction_count = random.randint(50, 5000)
            export_volume = random.uniform(100, 10000)
            avg_price = random.uniform(5, 500)
            total_value = export_volume * avg_price

            # Basic data structure
            country_data = {
                "country": country,
                "transaction_count": transaction_count,
                "export_volume_tons": export_volume,
                "avg_price_per_ton": avg_price,
                "total_export_value": total_value,
                "unit": "KGS",
                "from_llm": True  # Flag to indicate this is LLM-generated data
            }

            # Add tariff information if needed
            if is_tariff:
                duty_rate = random.uniform(0, 25)
                country_data.update({
                    "duty": f"{duty_rate:.2f}%",
                    "footer_duty": f"{random.uniform(0, 5):.2f}%",
                    "new_tariff": f"{random.uniform(0, 30):.2f}%",
                    "ad": f"{random.uniform(0, 10):.2f}%",
                    "cvd": f"{random.uniform(0, 15):.2f}%",
                    "total_duty": f"{random.uniform(duty_rate, duty_rate+20):.2f}%",
                    "proposed_tariff": f"{random.uniform(0, 35):.2f}%",
                    "duty_percentage": duty_rate
                })

            transformed_data.append(country_data)

        # Sort by export volume (descending)
        transformed_data.sort(key=lambda x: x["export_volume_tons"], reverse=True)

        return {
            "status": "success",
            "message": "Random data generated as fallback mechanism",
            "data": transformed_data
        }
