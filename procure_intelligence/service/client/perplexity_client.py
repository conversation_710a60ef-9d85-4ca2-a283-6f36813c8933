import json
import requests
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class PerplexityClient:
    """
    Client for interacting with the Perplexity API.
    """

    def __init__(self, api_key: str):
        """
        Initialize the Perplexity client with an API key.

        Args:
            api_key: Perplexity API key
        """
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.default_model = "sonar"

    def query(self, prompt: str, system_prompt: str = None, model: str = None) -> Dict[str, Any]:
        """
        Query the Perplexity API with a prompt.

        Args:
            prompt: The user prompt to send to the API
            system_prompt: Optional system prompt to set context
            model: Model to use (defaults to sonar)

        Returns:
            Dict containing the API response
        """
        if not self.api_key:
            logger.error("Perplexity API key not provided")
            return {"error": "API key not provided"}

        if not system_prompt:
            system_prompt = "You are a helpful assistant that provides accurate information."

        data = {
            "model": model or self.default_model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
        }

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Perplexity API request failed with status code: {response.status_code}")
                return {"error": f"API request failed with status code: {response.status_code}"}

        except Exception as e:
            logger.error(f"Error calling Perplexity API: {str(e)}")
            return {"error": f"Error calling API: {str(e)}"}

    def extract_json_from_response(self, response: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """
        Extract JSON data from a Perplexity API response.

        Args:
            response: The API response dictionary

        Returns:
            Extracted JSON data or None if extraction fails
        """
        if "error" in response:
            return None

        try:
            # Check if response has the expected structure
            if "choices" not in response or not response["choices"]:
                logger.error("Invalid response format: missing 'choices' field")
                return None

            if "message" not in response["choices"][0]:
                logger.error("Invalid response format: missing 'message' field")
                return None

            if "content" not in response["choices"][0]["message"]:
                logger.error("Invalid response format: missing 'content' field")
                return None

            content = response["choices"][0]["message"]["content"]

            # Try to find JSON in the response
            if "[" in content and "]" in content:
                start_idx = content.find("[")
                end_idx = content.rfind("]") + 1
                json_str = content[start_idx:end_idx]
                return json.loads(json_str)
            elif "{" in content and "}" in content:
                start_idx = content.find("{")
                end_idx = content.rfind("}") + 1
                json_str = content[start_idx:end_idx]
                # Wrap single object in a list for consistent return type
                return [json.loads(json_str)]
            else:
                # Try parsing the whole content as JSON
                parsed = json.loads(content)
                # Ensure we return a list
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed]

        except (KeyError, json.JSONDecodeError) as e:
            logger.error(f"Failed to extract JSON from response: {str(e)}")
            return None

    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Get structured information about a chemical product.

        Args:
            chemical_name: The name of the chemical
            chemical_application: The application of the chemical

        Returns:
            Dictionary with chemical information
        """
        prompt = f"""You are a helpful assistant specializing in chemical information.
        When provided with a chemical name '{chemical_name}' and its application '{chemical_application}', return a JSON object with the product_name,
        product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code)
        and product_application (other common uses).
        In case of multiple hs codes, return only the most specific one.
        Format your response as valid JSON only with no additional text."""

        system_prompt = "You are a helpful assistant that provides accurate chemical information. Output only valid JSON."

        response = self.query(prompt, system_prompt)
        json_data = self.extract_json_from_response(response)

        if not json_data:
            return {
                "product_name": chemical_name,
                "product_family": "Not found",
                "cas_number": "Not found",
                "hs_code": "Not found",
                "hts_number": "Not found",
                "product_application": chemical_application + " (user provided)"
            }

        # Ensure all required fields exist
        expected_fields = ["product_name", "product_family", "cas_number", "hs_code", "hts_number", "product_application"]
        for field in expected_fields:
            if field not in json_data:
                json_data[field] = "Not found"

        return json_data
