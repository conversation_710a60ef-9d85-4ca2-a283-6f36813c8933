import logging
from typing import Dict, Any, List, Optional, Tuple

from model.supplier import Supplier, SupplierLookupResult
from service.client.db_client import DatabaseClient
from service.client.llm_client import LLMClient

logger = logging.getLogger(__name__)

class SupplierService:
    """
    Service for supplier-related operations.
    """

    def __init__(self, db_client: DatabaseClient, llm_client: LLMClient = None):
        """
        Initialize the supplier service.

        Args:
            db_client: Database client for querying trade data
            llm_client: LLM client for generating data when DB data is insufficient
        """
        self.db_client = db_client
        self.llm_client = llm_client

    def _build_query_top_suppliers(self, hs_code: str, origin: str, destination: str, months: int = 12, chemical_name: str = None) -> Tuple[str, Tuple]:
        """
        Build query to retrieve top suppliers for a specific HS code, origin, and destination.

        Args:
            hs_code: HS code (will use first 4 digits)
            origin: Origin country
            destination: Destination country
            months: Number of months to look back
            chemical_name: Name of the chemical to search for

        Returns:
            Tuple of SQL query and parameters
        """
        # Extract first 4 digits of HS code
        hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

        # Initialize parameters list
        params = [hs_code_4digit, origin, destination, months]

        # Handle product description conditions if chemical_name is provided
        product_desc_condition = ""
        if chemical_name:
            # For now, just use the chemical name directly
            # In a more complete implementation, we would use chemical name variations
            like_conditions = []

            # Add the chemical name as a search term
            like_conditions.append("product_desc ILIKE %s")
            search_term = chemical_name
            if not search_term.startswith('%'):
                search_term = '%' + search_term
            if not search_term.endswith('%'):
                search_term = search_term + '%'
            params.append(search_term)

            if like_conditions:
                product_desc_condition = "AND (" + " OR ".join(like_conditions) + ")"

        query = f"""
        SELECT
            exporter_name,
            global_exporter_id,
            global_std_unit_id,
            SUM(std_qty) AS total_quantity,
            AVG(std_qty) AS average_quantity_per_shipment,
            AVG(fob_value_usd) AS average_fob,
            MIN(fob_value_usd) AS minimum_fob,
            COUNT(*) AS shipment_count
        FROM
            volza_trade_data
        WHERE
            SUBSTRING(hs_code, 1, 4) = %s
            AND parent_coo = %s
            AND parent_cod = %s
            AND date >= CURRENT_DATE - INTERVAL '%s month'
            {product_desc_condition}
        GROUP BY
            exporter_name, global_exporter_id, global_std_unit_id
        ORDER BY
            total_quantity DESC
        LIMIT 10
        """

        return query, tuple(params)

    def get_top_suppliers(self, hs_code: str, country: str, destination: str, chemical_name: str = None, months: int = 12) -> SupplierLookupResult:
        """
        Get top suppliers from a specific country for a given HS code.

        Args:
            hs_code: The HS code to search for
            country: The origin country to search for suppliers
            destination: The destination country
            chemical_name: The name of the chemical
            months: Number of months of data to retrieve

        Returns:
            SupplierLookupResult with status and data
        """
        # Format HS code (remove dots if present)
        formatted_hs_code = hs_code.replace(".", "")

        try:
            # Build and execute query
            query, params = self._build_query_top_suppliers(formatted_hs_code, country, destination, months, chemical_name)
            result = self.db_client.execute_query(query, params)

            # Transform API data to match expected structure
            transformed_data = []

            for item in result.get("data", []):
                # Handle potential missing or None values
                shipment_count = item.get("shipment_count", 0) or 0
                total_quantity = item.get("total_quantity", 0) or 0
                average_fob = item.get("average_fob", 0) or 0

                # Calculate total value based on average price and total quantity
                total_export_value = average_fob * total_quantity if average_fob and total_quantity else 0

                # Format the data to match the expected structure
                transformed_item = {
                    "name": item.get("exporter_name", "Unknown Supplier"),
                    "global_exporter_id": item.get("global_exporter_id", ""),
                    "transaction_count": shipment_count,
                    "export_volume_tons": float(total_quantity) if total_quantity else 0,
                    "avg_price_per_ton": float(average_fob) if average_fob else 0,
                    "total_export_value": total_export_value,
                    "unit": item.get("global_std_unit_id", "KGS"),
                    "average_quantity_per_shipment": item.get("average_quantity_per_shipment", 0) or 0
                }

                transformed_data.append(transformed_item)

            # Check if any suppliers have missing names
            missing_names = False
            for supplier in transformed_data:
                if supplier.get('name') == "-" or not supplier.get('name') or supplier.get('name', "").strip() == "":
                    missing_names = True
                    break

            # If we have missing names and we have the chemical name, try to get real supplier names
            if missing_names and chemical_name and self.llm_client:
                # Get supplier names using LLM
                llm_suppliers = self.llm_client.generate_supplier_data(
                    chemical_name,
                    hs_code,
                    country,
                    len(transformed_data)
                )

                # If we got valid LLM data, update the supplier names
                if llm_suppliers["status"] == "success" and llm_suppliers["data"]:
                    for i, supplier in enumerate(transformed_data):
                        if supplier.get('name') == "-" or not supplier.get('name') or supplier.get('name', "").strip() == "":
                            # Try to get a name from the LLM data
                            if i < len(llm_suppliers["data"]):
                                supplier['name'] = llm_suppliers["data"][i].get('name', supplier.get('name', "-"))

            # If no data was found, try to generate data using LLM
            if not transformed_data and self.llm_client:
                logger.info(f"No data found in database, generating with LLM for {chemical_name}, {hs_code}, {country}")

                # Generate data using LLM
                llm_result = self.llm_client.generate_supplier_data(
                    chemical_name,
                    hs_code,
                    country
                )

                if llm_result["status"] == "success":
                    return SupplierLookupResult(
                        status="success",
                        message="Data generated by LLM as fallback mechanism",
                        data=llm_result["data"]
                    )
                else:
                    return SupplierLookupResult(
                        status="warning",
                        message=f"No suppliers found in {country} for the given HS code.",
                        data=[]
                    )

            return SupplierLookupResult(
                status="success",
                data=transformed_data
            )

        except Exception as e:
            logger.error(f"Error in get_top_suppliers: {e}")

            # Try to generate data using LLM as fallback
            if self.llm_client:
                llm_result = self.llm_client.generate_supplier_data(
                    chemical_name,
                    hs_code,
                    country
                )

                if llm_result["status"] == "success":
                    return SupplierLookupResult(
                        status="success",
                        message="Data generated by LLM as fallback mechanism",
                        data=llm_result["data"]
                    )

            return SupplierLookupResult(
                status="error",
                message=f"Failed to get top suppliers: {str(e)}"
            )

    def get_supplier_details(self, supplier_name: str, hs_code: str, chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Get detailed information about a supplier.

        Args:
            supplier_name: The name of the supplier
            hs_code: The HS code of the product
            chemical_name: The name of the chemical
            country: The country of the supplier

        Returns:
            Dictionary with supplier details
        """
        # In a real implementation, we would query a database for supplier details
        # For now, we'll generate some placeholder data

        # If we have an LLM client, use it to generate more realistic data
        if self.llm_client:
            # This would be implemented in the LLM client
            # For now, return a placeholder
            return {
                "company_name": supplier_name,
                "overview": f"Supplier of {chemical_name} based in {country}",
                "contact": {
                    "email": "<EMAIL>",
                    "phone": "+1234567890",
                    "address": f"123 Chemical St, {country}",
                    "website": "https://example.com"
                },
                "certifications": ["ISO 9001", "ISO 14001"],
                "advantages": ["Competitive pricing", "High quality products", "Fast delivery"]
            }

        # Fallback to basic details
        return {
            "company_name": supplier_name,
            "overview": f"Supplier of {chemical_name} based in {country}",
            "contact": {
                "email": "Not available",
                "phone": "Not available",
                "address": "Not available"
            }
        }
