import re
from typing import Dict, Any, Optional, <PERSON><PERSON>

def validate_chemical_input(chemical_name: str, application: str) -> <PERSON><PERSON>[bool, Optional[str]]:
    """
    Validate chemical input parameters.
    
    Args:
        chemical_name: Name of the chemical
        application: Application of the chemical
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not chemical_name or not chemical_name.strip():
        return False, "Chemical name is required"
    
    if not application or not application.strip():
        return False, "Chemical application is required"
    
    # Check for potentially dangerous inputs (SQL injection, etc.)
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, chemical_name, re.IGNORECASE) or re.search(pattern, application, re.IGNORECASE):
            return False, "Invalid input detected"
    
    return True, None

def validate_hs_code(hs_code: str) -> <PERSON><PERSON>[bool, Optional[str]]:
    """
    Validate HS code format.
    
    Args:
        hs_code: HS code to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not hs_code or not hs_code.strip():
        return False, "HS code is required"
    
    # Remove dots and check if it's a valid HS code format
    clean_hs_code = hs_code.replace(".", "")
    
    # HS codes are typically 6-10 digits
    if not clean_hs_code.isdigit() or len(clean_hs_code) < 4 or len(clean_hs_code) > 10:
        return False, "Invalid HS code format. Expected 4-10 digits."
    
    return True, None

def validate_country(country: str) -> Tuple[bool, Optional[str]]:
    """
    Validate country name.
    
    Args:
        country: Country name to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not country or not country.strip():
        return False, "Country name is required"
    
    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, country, re.IGNORECASE):
            return False, "Invalid input detected"
    
    return True, None

def validate_months(months: int) -> Tuple[bool, Optional[str]]:
    """
    Validate months parameter.
    
    Args:
        months: Number of months to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(months, int):
        return False, "Months must be an integer"
    
    if months < 1 or months > 60:
        return False, "Months must be between 1 and 60"
    
    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.
    
    Args:
        input_str: Input string to sanitize
        
    Returns:
        Sanitized string
    """
    if not input_str:
        return ""
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)
    
    # Limit length
    return sanitized[:500]
