#!/usr/bin/env python3
"""
Script to compare database configurations and test connectivity between 
procure-intelligence and pricing-engine services.
"""

import os
import sys
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_pricing_engine_db_config():
    """Get pricing-engine database configuration."""
    return {
        'host': os.environ.get('DB_HOST'),
        'database': os.environ.get('DB_NAME'),
        'user': os.environ.get('DB_USER'),
        'password': os.environ.get('DB_PASSWORD'),
        'port': os.environ.get('DB_PORT')
    }

def get_procure_intelligence_db_config():
    """Get procure-intelligence database configuration."""
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', 5432),
        'database': os.getenv('DB_NAME', 'volza_trade_data'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'postgres')
    }

def test_db_connection(config, service_name):
    """Test database connection with given configuration."""
    try:
        # Convert port to int if it's a string
        if 'port' in config and config['port']:
            config['port'] = int(config['port'])
        
        conn = psycopg2.connect(**config)
        cursor = conn.cursor()
        
        # Test basic connectivity
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        # Test if volza_trade_data table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'volza_trade_data'
            );
        """)
        table_exists = cursor.fetchone()[0]
        
        # Get table row count if it exists
        row_count = 0
        if table_exists:
            cursor.execute("SELECT COUNT(*) FROM volza_trade_data;")
            row_count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return {
            'success': True,
            'version': version,
            'table_exists': table_exists,
            'row_count': row_count
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def compare_configurations():
    """Compare database configurations between services."""
    print("=" * 80)
    print("DATABASE CONFIGURATION COMPARISON")
    print("=" * 80)
    
    pricing_config = get_pricing_engine_db_config()
    procure_config = get_procure_intelligence_db_config()
    
    print("\n1. PRICING-ENGINE Configuration:")
    print("-" * 40)
    for key, value in pricing_config.items():
        print(f"  {key}: {value}")
    
    print("\n2. PROCURE-INTELLIGENCE Configuration:")
    print("-" * 40)
    for key, value in procure_config.items():
        print(f"  {key}: {value}")
    
    print("\n3. DIFFERENCES:")
    print("-" * 40)
    differences = []
    all_keys = set(pricing_config.keys()) | set(procure_config.keys())
    
    for key in all_keys:
        pricing_val = pricing_config.get(key)
        procure_val = procure_config.get(key)
        
        if pricing_val != procure_val:
            differences.append(f"  {key}: pricing='{pricing_val}' vs procure='{procure_val}'")
    
    if differences:
        for diff in differences:
            print(diff)
    else:
        print("  No differences found in configuration")
    
    return pricing_config, procure_config

def test_connectivity():
    """Test connectivity for both services."""
    print("\n" + "=" * 80)
    print("DATABASE CONNECTIVITY TEST")
    print("=" * 80)
    
    pricing_config, procure_config = compare_configurations()
    
    print("\n4. PRICING-ENGINE Connectivity:")
    print("-" * 40)
    pricing_result = test_db_connection(pricing_config, "pricing-engine")
    if pricing_result['success']:
        print(f"  ✓ Connection successful")
        print(f"  ✓ Database version: {pricing_result['version'][:50]}...")
        print(f"  ✓ volza_trade_data table exists: {pricing_result['table_exists']}")
        print(f"  ✓ Table row count: {pricing_result['row_count']:,}")
    else:
        print(f"  ✗ Connection failed: {pricing_result['error']}")
    
    print("\n5. PROCURE-INTELLIGENCE Connectivity:")
    print("-" * 40)
    procure_result = test_db_connection(procure_config, "procure-intelligence")
    if procure_result['success']:
        print(f"  ✓ Connection successful")
        print(f"  ✓ Database version: {procure_result['version'][:50]}...")
        print(f"  ✓ volza_trade_data table exists: {procure_result['table_exists']}")
        print(f"  ✓ Table row count: {procure_result['row_count']:,}")
    else:
        print(f"  ✗ Connection failed: {procure_result['error']}")
    
    # Compare results
    print("\n6. COMPARISON SUMMARY:")
    print("-" * 40)
    if pricing_result['success'] and procure_result['success']:
        if pricing_result['row_count'] == procure_result['row_count']:
            print("  ✓ Both services connect to the same database (same row count)")
        else:
            print(f"  ⚠ Different databases detected:")
            print(f"    Pricing-engine: {pricing_result['row_count']:,} rows")
            print(f"    Procure-intelligence: {procure_result['row_count']:,} rows")
    else:
        print("  ✗ Cannot compare - one or both connections failed")

if __name__ == "__main__":
    test_connectivity()
