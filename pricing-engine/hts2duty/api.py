import csv
import os.path
import redis

import sys
import os

from flask import Flask, request, jsonify

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.tariff_service import *  
from src.tariff.extract_hts import *
from src.agents.tariff_extractor_agent import *

app = Flask(__name__)

import redis

# Replace with your EC2 instance's IP if you're connecting remotely
redis_host = 'localhost'  # or 'your-ec2-public-ip'
redis_port = 6379         # default Redis port
redis_password = None     # Set to your Redis password if you have one

# Create a Redis connection
r = redis.Redis(host=redis_host, port=redis_port, password=redis_password, decode_responses=True)

# Flush redis cache at startup
r.flushall()
print("Redis flushed successfully")


def standardize_code_hts(code: str) -> str:
    # Remove all non-numeric characters
    numbers = ''.join(char for char in code if char.isdigit())
    
    # Pad with zeros if less than 10 digits
    if len(numbers) <= 12:
        numbers = numbers.ljust(10, '0')
    elif len(numbers) > 12:
        raise ValueError("Input contains more than 10 digits")
    
    # Format into the desired pattern
    return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"

def load_indices():
    print("Loading pre-computed indices...")
    indices = HTSIndices.load("hts_indices", auto_create=True, data_file='htsdata_all.json')
    lookup = HTSSemanticLookup(indices)
    return lookup

def get_tarff_country(lookup,hs_code,country):

        tf_service = TariffService()              
        print(hs_code)
        standardized_hts_code = standardize_code_hts(hs_code)  
        print(standardized_hts_code)
        tariff_results = lookup.lookup_hts(standardized_hts_code)
        print(f"tariff_results; {tariff_results}")
        tariff_results_list=[tariff_results]  

        Standard_duty, Incremental_duty, Proposed_duty, ad_value, cd_value = tf_service.get_country_tariff(tariff_results_list,country)  

        json_blob={}
        json_blob['Country']=country
        json_blob['Standard Tariff']=Standard_duty
        json_blob['New Tariff']=Incremental_duty
        json_blob['Proposed Tariff']=Proposed_duty
        json_blob['Anti-Dumping Duty'] = ad_value
        json_blob['Countervailing Duty'] = cd_value
        tariff_response = extract_tariff_info_structured_assist_openAI(json_blob) 
        return tariff_response 

lookup = None

@app.before_request
def initialize():
    global lookup
    if lookup is None:
        lookup = load_indices()

@app.route('/tariff', methods=['GET'])
def get_tariff():
    try:
        hs_code = request.args.get('hs_code')
        country = request.args.get('country')
        
        redis_key = str(hs_code)+str(country)
        
        if not hs_code or not country:
            return jsonify({"error": "Both hs_code and country parameters are required"}), 400
        
        val_ = r.get(redis_key)
        if val_:
            return jsonify(val_)
        tariff_response = get_tarff_country(lookup, hs_code, country)

        r.set(redis_key, str(tariff_response))

        return jsonify(tariff_response)
    
    except ValueError as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500

if __name__ == "__main__":
    # Load indices at startup
    lookup = load_indices()
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
               
