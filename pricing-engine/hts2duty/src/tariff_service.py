from typing import List
import json
import os
import yaml
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime

from src.tariff.hts_duty_calculator import *
from src.tariff.utils import *

load_dotenv()

DB_URL = os.getenv("DB_URL")
DB_USERNAME = os.getenv("DB_USERNAME")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")


class TariffService:
    def __init__(self):
        self.config = self._load_config()
        self.proposed_tariffs = self._load_proposed_tariffs()
        self.exempt_hs_codes = self._load_exempt_hs_codes()
        self.ad_cd_data = self._load_ad_cd_data()

    def _load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'tariff_config.yaml')
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)

    def _load_proposed_tariffs(self):
        csv_path = os.path.join(os.path.dirname(__file__), self.config['proposed_tariffs']['csv_path'])
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            return dict(zip(df['country_code'], df['tariff_percentage']))
        return {}

    def _load_exempt_hs_codes(self):
        exempt_path = os.path.join(os.path.dirname(__file__), 
                                 self.config['proposed_tariffs']['exempt_codes_path'])
        if os.path.exists(exempt_path):
            with open(exempt_path, 'r') as f:
                return {line.strip() for line in f if line.strip()}
        return set()

    def _load_ad_cd_data(self):
        csv_path = os.path.join(os.path.dirname(__file__), self.config['proposed_tariffs']['ad_cd_path'])
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            # Create a dictionary with (hs_code, country) as key and (AD, CD) as value
            return {(str(row['hs_code']), row['country']): (row['AD'], row['CD']) 
                   for _, row in df.iterrows()}
        return {}

    def _get_ad_cd_values(self, hs_code, country):
        # Clean HS code to match format in CSV
        cleaned_hs = ''.join(c for c in hs_code if c.isdigit())[:6]
        # Try to get AD/CD values, default to 0 if not found
        ad, cd = self.ad_cd_data.get((cleaned_hs, country), (0, 0))
        return ad, cd

    def _is_hs_code_exempt(self, hs_code):
        # Remove dots and spaces from HS code for comparison
        cleaned_hs = ''.join(c for c in hs_code if c.isdigit())
        # Take only first 8 digits for comparison
        cleaned_hs = cleaned_hs[:8]
        print(cleaned_hs)
        # Similarly clean and truncate the exempt codes for comparison
        exempt_codes_8digit = {code[:8] for code in self.exempt_hs_codes}
        return cleaned_hs in exempt_codes_8digit

    def get_country_tariff(self, tariff_results, country):
        country_code = country_codes[country]
        print(country_code)
        Standard_duty = parse_hts_data(tariff_results, country_code)
        Incremental_duty = "NA"
        Proposed_duty = "NA"
        
        # Get HS code from tariff results
        hs_code = tariff_results[0]['main_details']['htsno'] if tariff_results else None
        
        # Get AD/CD values
        ad_value, cd_value = self._get_ad_cd_values(hs_code, country_code) if hs_code else (0, 0)
        print(ad_value, cd_value)
        
        # Get incremental duty from config if it exists
        if country_code in self.config['incremental_duties']:
            Incremental_duty = self.config['incremental_duties'][country_code]['description']
            print(Incremental_duty)
        
        # Get proposed duty if it exists and HS code is not exempt
        if hs_code and country_code in self.proposed_tariffs:
            if not self._is_hs_code_exempt(hs_code):
                effective_date = self.config['proposed_tariffs']['effective_date']
                tariff_value = self.proposed_tariffs[country_code]
                Proposed_duty = f"Additional {tariff_value}% duty effective from {effective_date}"
                print(Proposed_duty)
            else:
                print(f"HS code {hs_code} is exempt from proposed tariffs")
            
        return Standard_duty, Incremental_duty, Proposed_duty, ad_value, cd_value



