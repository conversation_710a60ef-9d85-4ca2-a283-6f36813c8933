import json
def hts2hs(code_string):
    # Remove all dots from the string
    no_dots = code_string.replace(".", "")
    # Take first 6 digits
    truncated = no_dots[:10]#8 volza has both 8 and 10 digit string
    return truncated
def parse_hts_data_all_countries(json_data):
    """
    Parse HTS data and calculate duties for each country code and HTS number
    """
    duties_dict = {}
    
    for item_new in json_data:
        #print(item_new)
        item = item_new["main_details"]
        htsno = item.get('htsno')
        print(htsno)
        #exit(1)
        if not htsno:  # Skip entries without HTSNO
            continue
        
        product_description = item.get('description', '')
        general_duty = item.get('general', '')
        special_duty = item.get('special', '')
        other_duty = item.get('other', '')
        footnotes = item_new.get('footnote_references', [])
        #print(footnotes)
        #exit(1)
        
        # Get list of all country codes mentioned in special section
        # Get list of all country codes mentioned in special section
        country_codes = []
        if special_duty:
            # Extract country codes between parentheses
            if '(' in special_duty and ')' in special_duty:
                codes_section = special_duty.split('(')[1].split(')')[0]
                country_codes = [code.strip() for code in codes_section.split(',')]
        
        # Get anti-dumping duty from footnotes
        # Get anti-dumping duty from footnotes
        footer_duty = "NA"
        if footnotes:
            referenced_code = [note['referenced_code'] for note in footnotes if 'referenced_code' in note and note['referenced_code']]
            footer_duty = [f"Reference: {referenced_code}, Description: {note['details']['description']}, General: {note['details']['general']} " for note in footnotes 
                          if 'details' in note and note['details']]
            footer_duty = '; '.join(footer_duty) if footer_duty else "NA"
       
        #print(f"****{footer_duty}*********")
        #exit(1)
        # Calculate duties for each possible country code
        all_country_codes = list(set(country_codes))
        #all_country_codes.add("All Other Countries")
        all_country_codes.append("Countries with no special trade agreements")
        for country in all_country_codes:
            #if country and len(country) == 2:  # Only process 2-letter country codes
            key = (country, htsno)

            if country in ['CU','RU','KP',"BY"]:#'IR',
                duty_context_str = "This country does not belong among countries which have Normal Trade Relations with US"
                duties_dict[key] = {
                    "country":country,
                    'hts_code':htsno,
                    'hs_code': hts2hs(htsno),
                    'description' : product_description,
                    'duty_context': duty_context_str,
                    'duty rate': other_duty,
                    'footer_duty': "NA"  # Changed from anti_dumping_duty
                }
            else:
                duty_context_str = "This country belongs among countries which have Normal Trade Relations with US"
                duty_rate_value = general_duty

                if country in country_codes:
                    duty_context_str += "In addition, this country also qualify for preferential duty rates under a variety of special trade agreements with US and usually duty-free" 
                    duty_rate_value = "duty-free" 

                if country != "Countries with no special trade agreements":
                    footer_duty_str = "NA"  # Changed variable name
                else:
                    footer_duty_str = footer_duty  # Changed variable name

          
                duties_dict[key] = {
                    "country":country,
                    'hts_code':htsno,
                    'hs_code': hts2hs(htsno),
                    'description' : product_description,
                    'duty_context': duty_context_str,
                    'duty rate': duty_rate_value,
                    'footer_duty': footer_duty_str  # Changed from anti_dumping_duty
                }
        
                
        return duties_dict

def process_duties(input_data):
    """
    Main function to process the HTS data and return duties information
    """
    try:
        # Parse JSON data if it's a string
        if isinstance(input_data, str):
            data = json.loads(input_data)
        else:
            data = input_data
            
        # Calculate duties
        duties = parse_hts_data_all_countries(data)
        return duties
        
    except Exception as e:
        print(f"Error processing duties: {str(e)}")
        return None

# Example usage:
if __name__ == "__main__":
    #Test with a sample entry
    sample_data = [
        {
            "main_details": {
            "htsno": "3907.30.00.00",
            "description": "Epoxide resins",
            "units": [
                "kg"
            ],
            "general": "6.1%",
            "special": "Free (A*,AU,BH,CL,CO,D,E,IL,JO,K,KR,MA,OM,P,PA,PE,S,SG) ",
            "other": "15.4\u00a2/kg + 47%",
            "indent": "1",
            "superior": "null"
            },
            "footnote_references": [
            {
                "referenced_code": "9903.88.02",
                "footnote_value": "See 9903.88.02. ",
                "details": {
                "htsno": "9903.88.02",
                "description": "Except as provided in headings 9903.88.12, 9903.88.17, 9903.88.20, 9903.88.54, 9903.88.59, 9903.88.61, 9903.88.63, 9903.88.66, 9903.88.67, 9903.88.68, 9903.88.69, or 9903.88.70, articles the product of China, as provided for in U.S. note 20(c) to this subchapter and as provided for in the subheadings enumerated in U.S. note 20(d)",
                "general": "The duty provided in the applicable subheading + 25%",
                "special": "",
                "other": ""
                }
            }
            ],
            "hts_code": "3907.30.00.00",
            "similarity_score": 0.8901344537734985
        }        
    ]
    result = process_duties(sample_data)
    print(result)
    for (country, htsno), duties in result.items():
        print(f"\nCountry: {country}")
        print(f"HTSNO: {htsno}")
        print("Duties:", duties)
