#!/usr/bin/env python3
"""
Initialization script to ensure all required indices are created on first run.
This script should be run before starting the application to ensure all dependencies are met.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")))

from src.tariff.extract_hts import HTSIndices, HTSSemanticLookup

def initialize_all_indices():
    """Initialize all required indices for the hts2duty application."""
    
    print("=== HTS2Duty Indices Initialization ===")
    print("This script will create all required indices for the application.")
    print()
    
    # Required indices and their data files
    indices_config = [
        {
            'name': 'Main HTS Indices',
            'directory': 'hts_indices',
            'data_file': 'htsdata_all.json',
            'description': 'Primary HTS code lookup indices'
        },
        {
            'name': 'Tariff Database Indices', 
            'directory': 'hts_indices_htsTariff',
            'data_file': 'htsdata_all.json',
            'description': 'Indices for tariff database functionality'
        }
    ]
    
    # Check if main data file exists
    main_data_file = 'htsdata_all.json'
    if not os.path.exists(main_data_file):
        print(f"❌ Error: Required data file '{main_data_file}' not found!")
        print("Please ensure the HTS data file is present in the current directory.")
        return False
    
    print(f"✅ Found main data file: {main_data_file}")
    file_size = os.path.getsize(main_data_file) / (1024 * 1024)  # Size in MB
    print(f"   File size: {file_size:.1f} MB")
    print()
    
    success_count = 0
    total_count = len(indices_config)
    
    for config in indices_config:
        print(f"Initializing {config['name']}...")
        print(f"  Directory: {config['directory']}")
        print(f"  Description: {config['description']}")
        
        try:
            # Check if indices already exist
            if os.path.exists(config['directory']):
                indices_file = os.path.join(config['directory'], 'indices.pkl')
                raw_data_file = os.path.join(config['directory'], 'raw_data.json')
                
                if os.path.exists(indices_file) and os.path.exists(raw_data_file):
                    print(f"  ✅ Indices already exist - skipping creation")
                    
                    # Verify indices can be loaded
                    try:
                        indices = HTSIndices.load(config['directory'], auto_create=False)
                        print(f"     - Code index size: {len(indices.code_index)}")
                        print(f"     - Word index size: {len(indices.word_index)}")
                        print(f"     - Embeddings size: {len(indices.description_embeddings)}")
                        success_count += 1
                    except Exception as e:
                        print(f"  ⚠️  Warning: Existing indices appear corrupted: {e}")
                        print(f"     Attempting to recreate...")
                        # Remove corrupted indices and recreate
                        import shutil
                        shutil.rmtree(config['directory'])
                        indices = HTSIndices.load(config['directory'], auto_create=True, data_file=config['data_file'])
                        print(f"  ✅ Indices recreated successfully")
                        success_count += 1
                else:
                    print(f"  ⚠️  Directory exists but indices incomplete - recreating...")
                    indices = HTSIndices.load(config['directory'], auto_create=True, data_file=config['data_file'])
                    print(f"  ✅ Indices created successfully")
                    success_count += 1
            else:
                # Create new indices
                print(f"  📦 Creating new indices...")
                indices = HTSIndices.load(config['directory'], auto_create=True, data_file=config['data_file'])
                print(f"  ✅ Indices created successfully")
                print(f"     - Code index size: {len(indices.code_index)}")
                print(f"     - Word index size: {len(indices.word_index)}")
                print(f"     - Embeddings size: {len(indices.description_embeddings)}")
                success_count += 1
                
        except Exception as e:
            print(f"  ❌ Error creating indices: {e}")
            import traceback
            traceback.print_exc()
        
        print()
    
    # Summary
    print("=== Initialization Summary ===")
    print(f"Successfully initialized: {success_count}/{total_count} indices")
    
    if success_count == total_count:
        print("✅ All indices initialized successfully!")
        print()
        print("The hts2duty application is ready to run.")
        print("You can now start the application with:")
        print("  - python main.py (for command line testing)")
        print("  - python api.py (for API server)")
        return True
    else:
        print("❌ Some indices failed to initialize.")
        print("Please check the errors above and ensure all required data files are present.")
        return False

def verify_application_readiness():
    """Verify that the application is ready to run by testing core functionality."""
    
    print("\n=== Application Readiness Check ===")
    
    try:
        # Test main indices loading
        print("Testing main indices loading...")
        from main import load_indices
        lookup = load_indices()
        print("✅ Main indices loaded successfully")
        
        # Test a simple lookup
        print("Testing HTS code lookup...")
        test_result = lookup.lookup_hts("2933.59.90.00")
        if test_result:
            print("✅ HTS lookup functionality working")
        else:
            print("⚠️  HTS lookup returned None")
        
        # Test tariff service
        print("Testing tariff service...")
        from src.tariff_service import TariffService
        tf_service = TariffService()
        print("✅ Tariff service initialized successfully")
        
        print("\n✅ Application readiness check passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Application readiness check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting HTS2Duty initialization...")
    print()
    
    # Initialize indices
    indices_success = initialize_all_indices()
    
    if indices_success:
        # Verify application readiness
        app_ready = verify_application_readiness()
        
        if app_ready:
            print("\n🎉 HTS2Duty is fully initialized and ready to use!")
        else:
            print("\n⚠️  Indices created but application verification failed.")
            print("Please check the configuration and try running the application manually.")
    else:
        print("\n❌ Initialization failed. Please resolve the issues above.")
        sys.exit(1)
