# hts2duty

A comprehensive tariff and duty calculation system for HTS (Harmonized Tariff Schedule) codes.

## Features

- **Automatic Index Creation**: The system automatically creates missing indices on the first run
- **Multi-source Duty Calculation**: Calculates duties from multiple sources including tariff databases
- **Semantic Search**: Advanced semantic search capabilities for HTS codes and product descriptions
- **Country-specific Tariffs**: Returns standard, anti-dumping, and countervailing duties by country
- **API Interface**: RESTful API for integration with other systems

## Usage

### Basic Usage
Given an HS-code and country of interest, return standard and anti-dumping duty:
```python
python main.py
```

### API Usage
Start the API server:
```python
python api.py
```

Then make requests to:
```
GET /tariff?hs_code=2933.59.90.00&country=India
```

### Initialization
For first-time setup or to ensure all indices are created:
```python
python initialize_indices.py
```

## Automatic Index Creation

The system automatically creates missing indices on the first run. This includes:

- **Main HTS Indices** (`hts_indices/`): Primary HTS code lookup indices
- **Tariff Database Indices** (`hts_indices_htsTariff/`): Indices for tariff database functionality

### How it works:
1. When the application starts, it checks for required index files
2. If indices are missing, it automatically builds them from the source data (`htsdata_all.json`)
3. The system handles various file encodings and JSON parsing issues automatically
4. If the source data file has issues, it attempts to copy from existing indices when possible

## Requirements

- Python 3.9+
- Required packages (install with `pip install -r requirements.txt`)
- `htsdata_all.json` - HTS data file (must be present in the root directory)

## Installation

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Ensure `htsdata_all.json` is present in the root directory
4. Run initialization: `python initialize_indices.py`
5. Start the application: `python main.py` or `python api.py`

## Error Handling

The system includes robust error handling for:
- Missing data files
- Corrupted indices
- File encoding issues
- JSON parsing problems
- Missing dependencies

If indices become corrupted, simply delete the index directories and restart the application - they will be automatically recreated.