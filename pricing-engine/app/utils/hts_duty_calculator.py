import json

class HTSDutyUtils:

    def hts2hs(code_string):
        # Remove all dots from the string
        no_dots = code_string.replace(".", "")
        # Take first 6 digits
        truncated = no_dots[:10]#8 volza has both 8 and 10 digit string
        return truncated
        
    def parse_hts_data(json_data, country):
        """
        Parse HTS data and calculate duties for each country code and HTS number
        """
        for item_new in json_data:
            item = item_new["main_details"]
            htsno = item.get('htsno')
            print(htsno)
            if not htsno:  # Skip entries without HTSNO
                continue
            
            product_description = item.get('description', '')
            general_duty = item.get('general', '')
            special_duty = item.get('special', '')
            other_duty = item.get('other', '')
            footnotes = item_new.get('footnote_references', [])
            
            # Get list of all country codes mentioned in special section
            country_codes = []
            if special_duty:
                # Extract country codes between parentheses
                if '(' in special_duty and ')' in special_duty:
                    codes_section = special_duty.split('(')[1].split(')')[0]
                    country_codes = [code.strip() for code in codes_section.split(',')]
            
            # Get footer duty from footnotes - only for China
            footer_duty = "NA"
            if footnotes and country == "CN":
                referenced_code = [note['referenced_code'] for note in footnotes if 'referenced_code' in note and note['referenced_code']]
                footer_duty = [f"Reference: {referenced_code}, Description: {note['details']['description']}, General: {note['details']['general']} " for note in footnotes 
                            if 'details' in note and note['details']]
                footer_duty = '; '.join(footer_duty) if footer_duty else "NA"
            
            # Calculate duties for each possible country code
            if country and len(country) == 2:  # Only process 2-letter country codes
                old_logic = 0
                if old_logic == 1:
                    duties_dict = {
                        "country": country,
                        'hts_code': htsno,
                        'hs_code': HTSDutyUtils.hts2hs(htsno),
                        'description': product_description,
                        'general_duty': 'Free' if country in country_codes else general_duty,
                        'additional_duty': other_duty if country in ['CU','RU','KP','IR',"BY"] else "Not Applicable",
                        'additional duty for China': footer_duty
                    }
                    return duties_dict
                else:
                    if country in ['CU','RU','KP',"BY"]:#'IR',
                        duty_context_str = "This country does not belong among countries which have Normal Trade Relations with US"
                        duties_dict = {
                            "country": country,
                            'hts_code': htsno,
                            'hs_code': HTSDutyUtils.hts2hs(htsno),
                            'description': product_description,
                            'duty_context': duty_context_str,
                            'duty rate': other_duty,
                            'additional duty for China': footer_duty
                        }
                        return duties_dict
                    else:
                        duty_context_str = "This country belongs among countries which have Normal Trade Relations with US"
                        duty_rate_value = general_duty
                        if country in country_codes:
                            duty_context_str += "In addition, this country also qualify for preferential duty rates under a variety of special trade agreements with US and usually duty-free" 
                            duty_rate_value = "duty-free"
                            footer_duty = "NA"
                        duties_dict = {
                            "country": country,
                            'hts_code': htsno,
                            'hs_code': HTSDutyUtils.hts2hs(htsno),
                            'description': product_description,
                            'duty_context': duty_context_str,
                            'duty rate': duty_rate_value,
                            'additional duty for China': footer_duty
                        }
                        return duties_dict                
        return None

    def parse_hts_data_all_countries(json_data):
        # Complete list of ISO 3166-1 alpha-2 country codes
        super_set_all_country_codes = [
            'AF', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 
            'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 
            'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BA', 'BW', 'BV', 'BR', 
            'IO', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 
            'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CG', 'CD', 
            'CK', 'CR', 'CI', 'HR', 'CU', 'CY', 'CZ', 'DK', 'DJ', 'DM', 
            'DO', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 
            'FJ', 'FI', 'FR', 'GF', 'PF', 'TF', 'GA', 'GM', 'GE', 'DE', 
            'GH', 'GI', 'GR', 'GL', 'GD', 'GP', 'GU', 'GT', 'GN', 'GW', 
            'GY', 'HT', 'HM', 'VA', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 
            'IR', 'IQ', 'IE', 'IL', 'IT', 'JM', 'JP', 'JO', 'KZ', 'KE', 
            'KI', 'KP', 'KR', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 
            'LY', 'LI', 'LT', 'LU', 'MO', 'MG', 'MW', 'MY', 'MV', 'ML', 
            'MT', 'MH', 'MQ', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 
            'MN', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'NC', 
            'NZ', 'NI', 'NE', 'NG', 'NU', 'NF', 'MK', 'MP', 'NO', 'OM', 
            'PK', 'PW', 'PS', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 
            'PT', 'PR', 'QA', 'RE', 'RO', 'RU', 'RW', 'SH', 'KN', 'LC', 
            'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'SC', 'SL', 'SG', 
            'SK', 'SI', 'SB', 'SO', 'ZA', 'GS', 'ES', 'LK', 'SD', 'SR', 
            'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TL', 
            'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'UG', 
            'UA', 'AE', 'GB', 'US', 'UM', 'UY', 'UZ', 'VU', 'VE', 'VN', 
            'VG', 'VI', 'WF', 'EH', 'YE', 'ZM', 'ZW'
        ]   
        def filter_country_codes(all_codes, excluded_codes):
            return [code for code in all_codes if code not in excluded_codes] 
        """
        Parse HTS data and calculate duties for each country code and HTS number
        """
        duties_dict = {}
        
        for item_new in json_data:
            #print(item_new)
            item = item_new["main_details"]
            htsno = item.get('htsno')
            print(htsno)
            #exit(1)
            if not htsno:  # Skip entries without HTSNO
                continue
            
            product_description = item.get('description', '')
            general_duty = item.get('general', '')
            special_duty = item.get('special', '')
            other_duty = item.get('other', '')
            footnotes = item_new.get('footnote_references', [])
            #print(footnotes)
            #exit(1)
            
            # Get list of all country codes mentioned in special section
            # Get list of all country codes mentioned in special section
            country_codes = []
            if special_duty:
                # Extract country codes between parentheses
                if '(' in special_duty and ')' in special_duty:
                    codes_section = special_duty.split('(')[1].split(')')[0]
                    country_codes = [code.strip() for code in codes_section.split(',')]
            
            # Get anti-dumping duty from footnotes
            # Get anti-dumping duty from footnotes
            anti_dumping = "NA"
            if footnotes:
                referenced_code = [note['referenced_code'] for note in footnotes if 'referenced_code' in note and note['referenced_code']]
                anti_dumping = [f"Reference: {referenced_code}, Description: {note['details']['description']}, General: {note['details']['general']} " for note in footnotes 
                            if 'details' in note and note['details']]
                anti_dumping = '; '.join(anti_dumping) if anti_dumping else "NA"
        
            #print(f"****{anti_dumping}*********")
            #exit(1)
            # Calculate duties for each possible country code
            all_country_codes = list(set(country_codes)) #special agreement 

            #all_country_codes.add("All Other Countries")
            # Get the filtered list
            print(len(super_set_all_country_codes))
            print(len(all_country_codes))
            print("*")
            filtered_country_codes = filter_country_codes(super_set_all_country_codes, all_country_codes)
            print(len(filtered_country_codes))
            print("**")
            exceptional_countries = ['CU','RU','KP',"BY"]
            exceptional_countries_china = exceptional_countries.copy()
            exceptional_countries_china.append("CN")
            print(exceptional_countries)
            print(exceptional_countries_china)
            filtered_country_codes = filter_country_codes(filtered_country_codes, exceptional_countries_china)
            print(f"filtered_country_codes is of length={len(filtered_country_codes)}")
            #exit(1)
            #all_country_codes.append("All remaining Countries with no special trade agreements with US. ")
            all_country_codes.append(str(exceptional_countries))
            all_country_codes.append("CN")
            all_country_codes.append(str(filtered_country_codes))
            print(all_country_codes)
            #exit(1)

            for country in all_country_codes:
                #if country and len(country) == 2:  # Only process 2-letter country codes
                key = (country, htsno)

                if country == str(['CU','RU','KP',"BY"]):#'IR',
                    duty_context_str = "Countries which DO NOT have Normal Trade Relations with US. "
                    duties_dict[key] = {
                        "country":country,
                        'hts_code':htsno,
                        'hs_code': HTSDutyUtils.hts2hs(htsno),
                        'description' : product_description,
                        'duty_context': duty_context_str,
                        'duty rate': other_duty,
                        #'anti_dumping_duty': "NA" #anti_dumping
                        'additional duty for China' : "NA"
                    }
                else:
                    duty_context_str = ""
                    duty_rate_value = general_duty

                    if country in country_codes:
                        duty_context_str += "This country qualify for preferential duty rates under a variety of special trade agreements with US and is usually duty-free" 
                        duty_rate_value = "duty-free" 
                    else:
                        duty_context_str = "Countries which have Normal Trade Relations with US. "
                        duty_rate_value = general_duty                    

                    # if country != str(filtered_country_codes): # "Countries with no special trade agreements":
                    #     anti_dumping_str = "NA"  
                    # else:
                    anti_dumping_str = "NA"  
                    if country == "CN":
                        anti_dumping_str = anti_dumping 

            
                    duties_dict[key] = {
                        "country":country,
                        'hts_code':htsno,
                        'hs_code': HTSDutyUtils.hts2hs(htsno),
                        'description' : product_description,
                        'duty_context': duty_context_str,
                        'duty rate': duty_rate_value,
                        #'anti_dumping_duty': anti_dumping_str,
                        'additional duty for China': anti_dumping_str
                    }
            
                    
            return duties_dict

    
