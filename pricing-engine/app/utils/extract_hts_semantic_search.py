import json
from typing import Dict, List, Optional, Any, Set, Tuple
import re
from collections import defaultdict
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

class HTSSemanticLookup:
    def __init__(self, data: List[dict], model_name: str = 'all-MiniLM-L6-v2'):
        """
        Initialize the HTS lookup system with data and build indices.
        
        Args:
            data (List[dict]): List of HTS code entries
            model_name (str): Name of the sentence transformer model to use
        """
        self.data = data
        self.code_index = {}  # HTS code to position/details
        self.word_index = defaultdict(set)  # Word to set of HTS codes
        self.description_embeddings = {}  # HTS code to description embedding
        
        # Initialize the sentence transformer model
        self.model = SentenceTransformer(model_name)
        
        # Build all indices
        self._build_indices()
        
    def _build_indices(self):
        """Build both traditional and semantic indices."""
        descriptions = []
        hts_codes = []
        
        for position, item in enumerate(self.data):
            if item.get('htsno'):
                hts_code = item['htsno']
                description = item.get('description', '')
                
                # Build code index
                self.code_index[hts_code] = {
                    'position': position,
                    'description': description,
                    'general': item.get('general', ''),
                    'indent': item.get('indent', ''),
                    'units': item.get('units', [])
                }
                
                # Build word index from description
                if description:
                    # Clean and tokenize description
                    cleaned_desc = re.sub(r'[^\w\s]', ' ', description.lower())
                    words = [w for w in cleaned_desc.split() if w]
                    
                    # Add each word to the index
                    for word in words:
                        self.word_index[word].add(hts_code)
                    
                    # Collect descriptions for batch embedding
                    descriptions.append(description)
                    hts_codes.append(hts_code)
        
        # Generate embeddings in batch
        if descriptions:
            embeddings = self.model.encode(descriptions, convert_to_tensor=True)
            for hts_code, embedding in zip(hts_codes, embeddings):
                self.description_embeddings[hts_code] = embedding
    
    def semantic_search(self, 
                       query: str, 
                       max_results: int = 10, 
                       min_similarity: float = 0.3) -> List[dict]:
        """
        Search for HTS codes using semantic similarity.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results to return
            min_similarity (float): Minimum similarity score threshold
            
        Returns:
            List[dict]: List of matching HTS entries with similarity scores
        """
        # Generate query embedding
        query_embedding = self.model.encode(query, convert_to_tensor=True)
        
        # Calculate similarities with all descriptions
        similarities = []
        for hts_code, embedding in self.description_embeddings.items():
            similarity = cosine_similarity(
                query_embedding.cpu().numpy().reshape(1, -1),
                embedding.cpu().numpy().reshape(1, -1)
            )[0][0]
            
            if similarity >= min_similarity:
                similarities.append((hts_code, similarity))
        
        # Sort by similarity score
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Build detailed results
        results = []
        for hts_code, similarity in similarities[:max_results]:
            result_temp = self.lookup_hts(hts_code)
            result_temp['hts_code']= hts_code
            result_temp['similarity_score'] = float(similarity)
            results.append(result_temp)
            # item = self.data[self.code_index[hts_code]['position']]
            # results.append({
            #     'hts_code': hts_code,
            #     'description': item.get('description', ''),
            #     'similarity_score': float(similarity),
            #     'general': item.get('general', ''),
            #     'special': item.get('special', ''),
            #     'other': item.get('other', '')
            # })
        
        return results
    
    def hybrid_search(self, 
                     query: str, 
                     max_results: int = 10,
                     semantic_weight: float = 0.7) -> List[dict]:
        """
        Perform hybrid search combining keyword and semantic matching.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results to return
            semantic_weight (float): Weight given to semantic similarity (0-1)
            
        Returns:
            List[dict]: List of matching HTS entries with combined scores
        """
        # Get semantic search results
        semantic_results = self.semantic_search(
            query, 
            max_results=max_results * 2  # Get more results for combining
        )
        
        # Get keyword search results
        keyword_results = self.search_by_description(
            query, 
            max_results=max_results * 2
        )
        
        # Combine results with weighted scoring
        combined_scores = defaultdict(float)
        
        # Add semantic scores
        for result in semantic_results:
            combined_scores[result['hts_code']] += result['similarity_score'] * semantic_weight
            
        # Add keyword scores
        keyword_weight = 1 - semantic_weight
        for result in keyword_results:
            combined_scores[result['hts_code']] += (result['match_score'] / len(query.split())) * keyword_weight
        
        # Sort and build final results
        sorted_results = sorted(
            combined_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:max_results]
        
        results = []
        for hts_code, score in sorted_results:
            result_temp = self.lookup_hts(hts_code)
            result_temp['combined_score'] = float(score),
            results.append(result_temp)            
            # item = self.data[self.code_index[hts_code]['position']]
            # results.append({
            #     'hts_code': hts_code,
            #     'description': item.get('description', ''),
            #     'combined_score': float(score),
            #     'general': item.get('general', ''),
            #     'special': item.get('special', ''),
            #     'other': item.get('other', '')
            # })
        
        return results
    
    def search_by_description(self, query: str, max_results: int = 10) -> List[dict]:
        """Traditional keyword-based search (kept from previous version)"""
        cleaned_query = re.sub(r'[^\w\s]', ' ', query.lower())
        query_words = [w for w in cleaned_query.split() if w]
        
        matching_codes = defaultdict(int)
        for word in query_words:
            for hts_code in self.word_index.get(word, set()):
                matching_codes[hts_code] += 1
        
        sorted_results = sorted(
            matching_codes.items(),
            key=lambda x: (-x[1], x[0])
        )[:max_results]
        
        results = []
        for hts_code, score in sorted_results:
            result_temp = self.lookup_hts(hts_code)
            result_temp['hts_code']= hts_code
            result_temp['match_score'] = float(score)
            results.append(result_temp)

            # result_temp['hts_code']= hts_code
            # item = self.data[self.code_index[hts_code]['position']]
            # results.append({
            #     'hts_code': hts_code,
            #     'description': item.get('description', ''),
            #     'match_score': score,
            #     'match_percentage': (score / len(query_words)) * 100,
            #     'general': item.get('general', ''),
            #     'special': item.get('special', ''),
            #     'other': item.get('other', '')
            # })
        
        return results

    def lookup_hts(self, hts_code: str) -> Optional[dict]:
        """Original HTS code lookup functionality (kept from previous version)"""
        if hts_code not in self.code_index:
            return None
            
        item = self.data[self.code_index[hts_code]['position']]
        
        result = {
            'main_details': {
                'htsno': item.get('htsno', ''),
                'description': item.get('description', ''),
                'units': item.get('units', []),
                'general': item.get('general', ''),
                'special': item.get('special', ''),
                'other': item.get('other', ''),
                'indent': item.get('indent', ''),
                'superior': item.get('superior', '')
            },
            'footnote_references': []
        }
        
        if item.get('footnotes'):
            for footnote in item['footnotes']:
                if isinstance(footnote, dict) and 'columns' in footnote:
                    if 'general' in footnote['columns']:
                        footnote_value = footnote.get('value', '')
                        if 'See ' in footnote_value:
                            referenced_hts = footnote_value.split('See ')[1].strip('.')
                            referenced_hts = ".".join(referenced_hts.split(".")[:-1])
                            if referenced_hts in self.code_index:
                                ref_position = self.code_index[referenced_hts]['position']
                                ref_item = self.data[ref_position]
                                result['footnote_references'].append({
                                    'referenced_code': referenced_hts,
                                    'footnote_value': footnote_value,
                                    'details': {
                                        'htsno': ref_item.get('htsno', ''),
                                        'description': ref_item.get('description', ''),
                                        'general': ref_item.get('general', ''),
                                        'special': ref_item.get('special', ''),
                                        'other': ref_item.get('other', '')
                                    }
                                })
        
        return result

    def get_index_stats(self) -> dict:
        """Get statistics about all indices"""
        return {
            'total_entries': len(self.data),
            'indexed_hts_codes': len(self.code_index),
            'indexed_words': len(self.word_index),
            'embedded_descriptions': len(self.description_embeddings),
            'average_codes_per_word': sum(len(codes) for codes in self.word_index.values()) / len(self.word_index) if self.word_index else 0,
            'memory_usage': {
                'code_index_size': len(self.code_index),
                'word_index_size': len(self.word_index),
                'embedding_index_size': len(self.description_embeddings)
            }
        }