from sqlalchemy import Integer, String, func
from app import db

class ChatHistory(db.Model):
    __tablename__ = 'chat_history'
    id = db.Column(Integer, primary_key=True, autoincrement=True)
    session_id = db.Column(String(255), nullable=False)
    sender_type = db.Column(String(50), nullable=False)
    message = db.Column(String(5000), nullable=False)
    request_headers = db.Column(String(5000), nullable=True)
    created_at = db.Column(db.DateTime, server_default=func.now())

    def __init__(self, session_id, sender_type, message, request_headers=None):
        self.session_id = session_id
        self.sender_type = sender_type
        self.message = message
        self.request_headers = request_headers

    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'sender_type': self.sender_type,
            'message': self.message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }