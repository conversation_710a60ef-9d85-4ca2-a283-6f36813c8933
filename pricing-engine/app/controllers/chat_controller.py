
import datetime
import json
import logging
import re
import uuid
from typing import Optional, <PERSON><PERSON>

from flask import Flask, request, g
from flask.views import MethodView
from flask_restx import Namespace, Resource, fields
from app import db, api

from langchain_openai.chat_models import ChatOpenAI
from langchain_community.utilities import SQLDatabase
from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool
from langchain_core.tools import Tool
from langchain_community.agent_toolkits.sql.base import create_sql_agent
from langchain.agents import AgentType
from app.entities.chat_history import ChatHistory
from app.services.pricing_service import PricingService

logger = logging.getLogger(__name__)

# ============================================================================
# INTEGRATED VALIDATION FUNCTIONS
# ============================================================================

def validate_session_id(session_id: str) -> Tuple[bool, Optional[str]]:
    """
    Validate session ID format.

    Args:
        session_id: Session ID to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not session_id or not session_id.strip():
        return True, None  # Session ID is optional

    # UUID format validation (with or without hyphens)
    uuid_pattern = r'^[a-fA-F0-9]{8}-?[a-fA-F0-9]{4}-?[a-fA-F0-9]{4}-?[a-fA-F0-9]{4}-?[a-fA-F0-9]{12}$'
    if not re.match(uuid_pattern, session_id):
        return False, "Invalid session ID format"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, session_id, re.IGNORECASE):
            return False, "Invalid session ID input detected"

    return True, None

def validate_message(message: str) -> Tuple[bool, Optional[str]]:
    """
    Validate message content.

    Args:
        message: Message content to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not message or not message.strip():
        return False, "Message is required"

    # Check message length
    if len(message) > 5000:
        return False, "Message is too long (maximum 5000 characters)"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, message, re.IGNORECASE):
            return False, "Invalid message content detected"

    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.

    Args:
        input_str: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_str:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)

    # Limit length
    return sanitized[:500]

ns = Namespace(name='chat', path="/v1", description='Chat API')
@ns.route('/chat', methods=['POST'])
class ChatAPI(Resource):
    """
    API resource for chat.
    """
    chat_message_model = ns.model('ChatMessage', {
        'session_id': fields.String(required=False, description='Session ID'),
        'message': fields.String(required=True, description='Message'),
    })
    def __init__(self, api=None, *args, **kwargs):
        super().__init__(api, *args, **kwargs)
        self.pricing_service = PricingService()

    @ns.doc('start_chat')
    @ns.expect(chat_message_model, validate=True)
    def post(self):
        """
        Chat Api.
        """
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /chat")

        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate inputs
        session_id = data.get('session_id', '')
        message = data.get('message', '')

        # Validate session ID if provided
        if session_id:
            is_valid, error_message = validate_session_id(session_id)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Session ID validation failed: {error_message}")
                return {'error': error_message}, 400
        else:
            session_id = str(uuid.uuid4())

        # Validate message
        is_valid, error_message = validate_message(message)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Message validation failed: {error_message}")
            return {'error': error_message}, 400

        # Sanitize inputs
        session_id = sanitize_input(session_id)
        message = sanitize_input(message)

        try:
            headers = request.headers
            headers_json = json.dumps(dict(headers), indent=2)
            logger.info(f"Request [ID: {request_id}] - Processing message: {message[:100]}...")

            response = self.pricing_service.tariff_iq(message)

            # Save chat history
            user_chat = ChatHistory(session_id=session_id, sender_type='user', message=message, request_headers=headers_json)
            bot_chat = ChatHistory(session_id=session_id, sender_type='ai', message=response)
            db.session.add_all([user_chat, bot_chat])
            db.session.commit()

            logger.info(f"Request [ID: {request_id}] - Successful response")
            return {'session_id': session_id, 'message': response}, 200

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {'error': f"Error processing chat message: {str(e)}"}, 500


@ns.route('/chat-sessions/<string:session_id>/summary', methods=['GET'])
class ChatSummaryApi(Resource):
    """
    API resource for chat summary.
    """
    @ns.doc('Chat Summary')
    def get(self, session_id):
        """
        Chat Summary Api.
        """
        return {'session_id':session_id, "summary": "A Version 4 UUID is a universally unique identifier that is generated using random numbers. The Version 4 UUIDs produced by this site were generated using a secure random number generator."}, 200

api.add_namespace(ns=ns)






# from flask import Flask, request, g
# from flask.views import MethodView
# from flask_restx import Namespace, Resource, fields
# from app import db, api

# from langchain_openai.chat_models import ChatOpenAI
# from langchain_community.utilities import SQLDatabase
# from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool
# from langchain_core.tools import Tool
# from langchain_community.agent_toolkits.sql.base import create_sql_agent
# from langchain.agents import AgentType
# from app.entities.chat_history import ChatHistory
# from app.services.pricing_service import PricingService
# import json
# import uuid



# ns = Namespace(name='chat', path="/v1", description='Chat API')
# @ns.route('/chat', methods=['POST'])
# class ChatAPI(Resource):
#     """
#     API resource for chat.
#     """
#     chat_message_model = ns.model('ChatMessage', {
#         'session_id': fields.String(required=False, description='Session ID'),
#         'message': fields.String(required=True, description='Message'),
#     })
#     def __init__(self, api=None, *args, **kwargs):
#         super().__init__(api, *args, **kwargs)
#         self.pricing_service = PricingService()

#     @ns.doc('start_chat')
#     @ns.expect(chat_message_model, validate=True)
#     def post(self):
#         """
#         Chat Api.
#         """
#         data = request.get_json()
#         session_id = data.get('session_id') or str(uuid.uuid4())
#         message = data.get('message')
#         headers = request.headers
#         headers_json = json.dumps(dict(headers), indent=2)
#         print(headers_json)
#         print("Raw Question:" + message)
#         response = self.pricing_service.tariff_iq(message)
#         # # Save chat history
#         user_chat = ChatHistory(session_id=session_id, sender_type='user', message=message, request_headers=headers_json)
#         bot_chat = ChatHistory(session_id=session_id, sender_type='ai', message=response)
#         db.session.add_all([user_chat, bot_chat])
#         db.session.commit()

#         return {'session_id':session_id, 'message': response}, 200


# @ns.route('/chat-sessions/<string:session_id>/summary', methods=['GET'])
# class ChatSummaryApi(Resource):
#     """
#     API resource for chat summary.
#     """
#     @ns.doc('Chat Summary')
#     def get(self, session_id):
#         """
#         Chat Summary Api.
#         """
#         return {'session_id':session_id, "summary": "A Version 4 UUID is a universally unique identifier that is generated using random numbers. The Version 4 UUIDs produced by this site were generated using a secure random number generator."}, 200

# api.add_namespace(ns=ns)
