"""Controller for service health check endpoints."""

import logging
import psutil
import datetime
from flask_restx import Namespace, Resource, fields
from app import api

logger = logging.getLogger(__name__)

ns = Namespace(name='health', path="/health", description='Service Health Check APIs')

health_response = api.model('HealthResponse', {
    'status': fields.String(required=True, description='Service status'),
    'timestamp': fields.DateTime(required=True, description='Current timestamp'),
    'version': fields.String(required=True, description='Service version'),
    'database': fields.Boolean(required=True, description='Database connection status'),
    'memory_usage': fields.Float(required=True, description='Memory usage percentage'),
    'cpu_usage': fields.Float(required=True, description='CPU usage percentage'),
    'uptime_seconds': fields.Integer(required=True, description='Service uptime in seconds')
})

start_time = datetime.datetime.now()

@ns.route('')
class HealthCheck(Resource):
    @ns.marshal_with(health_response)
    def get(self):
        """Get service health status."""
        # try:
        #     # Check database connection
        #     db.session.execute("SELECT 1")
        #     db_status = True
        # except Exception as e:
        #     logger.error(f"Database health check failed: {str(e)}")
        #     db_status = False

        # System metrics
        memory = psutil.virtual_memory()
        cpu = psutil.cpu_percent(interval=1)
        
        # Calculate uptime
        uptime = (datetime.datetime.now() - start_time).total_seconds()

        response = {
            'status': 'healthy',# if db_status else 'degraded',
            'timestamp': datetime.datetime.now(),
            'version': '1.0.0',  # You can make this dynamic using environment variables
            # 'database': db_status,
            'memory_usage': memory.percent,
            'cpu_usage': cpu,
            'uptime_seconds': int(uptime)
        }

        # if not db_status:
        #     return response, 503  # Service Unavailable
        
        return response, 200

@ns.route('/liveness')
class LivenessCheck(Resource):
    def get(self):
        """Basic liveness check."""
        return {'status': 'alive'}, 200

@ns.route('/readiness')
class ReadinessCheck(Resource):
    def get(self):
        """Check if service is ready to handle requests."""
        return {'status': 'ready'}, 200
        # try:
        #     # Check database connection
        #     db.session.execute("SELECT 1")
        #     return {'status': 'ready'}, 200
        # except Exception as e:
        #     logger.error(f"Readiness check failed: {str(e)}")
        #     return {'status': 'not ready', 'reason': 'database connection failed'}, 503

api.add_namespace(ns)