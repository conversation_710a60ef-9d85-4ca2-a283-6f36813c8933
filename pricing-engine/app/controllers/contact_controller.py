
import datetime
import json
import logging
import re
from typing import Optional, <PERSON><PERSON>

from flask import request, g
from flask_restx import Namespace, Resource, fields
from app import api
from app.services import email_client_instance
from app.config import EMAIL_RECEIVER

logger = logging.getLogger(__name__)

# ============================================================================
# INTEGRATED VALIDATION FUNCTIONS
# ============================================================================

def validate_email(email: str) -> Tuple[bool, Optional[str]]:
    """
    Validate email format.

    Args:
        email: Email address to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not email or not email.strip():
        return False, "Email is required"

    # Basic email format validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, "Invalid email format"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, email, re.IGNORECASE):
            return False, "Invalid email input detected"

    return True, None

def validate_phone(phone: str) -> Tuple[bool, Optional[str]]:
    """
    Validate phone number format.

    Args:
        phone: Phone number to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not phone or not phone.strip():
        return False, "Phone number is required"

    # Remove common phone number characters for validation
    clean_phone = re.sub(r'[\s\-\(\)\+\.]', '', phone)

    # Check if it contains only digits after cleaning
    if not clean_phone.isdigit():
        return False, "Phone number should contain only digits, spaces, hyphens, parentheses, and plus sign"

    # Check length (international phone numbers are typically 7-15 digits)
    if len(clean_phone) < 7 or len(clean_phone) > 15:
        return False, "Phone number should be between 7 and 15 digits"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, phone, re.IGNORECASE):
            return False, "Invalid phone input detected"

    return True, None

def validate_message(message: str) -> Tuple[bool, Optional[str]]:
    """
    Validate message content.

    Args:
        message: Message content to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not message or not message.strip():
        return False, "Message is required"

    # Check message length
    if len(message) > 5000:
        return False, "Message is too long (maximum 5000 characters)"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, message, re.IGNORECASE):
            return False, "Invalid message content detected"

    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.

    Args:
        input_str: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_str:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)

    # Limit length
    return sanitized[:500]

ns = Namespace(name='contact', path="/v1", description='Contact API')


@ns.route('/contact', methods=['POST'])
class ContactAPI(Resource):
    """
        API resource for contact.
    """
    contact_form_model = ns.model('ContactForm', {
        'session_id': fields.String(required=False, description='Session ID'),
        'message': fields.String(required=False, description='Message'),
        'name': fields.String(required=True, description='Name'),
        'company': fields.String(required=True, description='Company'),
        'phone': fields.String(required=True, description='Phone'),
        'email': fields.String(required=True, description='Email'),
        'annualVolume': fields.String(required=True, description='Annual Volume'),
        'selectedQuestion': fields.String(required=False, description='Selected Question')
    })

    @ns.doc('contact_form')
    @ns.expect(contact_form_model, validate=True)
    def post(self):
        """
        Contact Api.
        """
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /contact")

        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Contact form submission")

        # Extract and validate inputs
        session_id = data.get('session_id', '')
        message = data.get('message', '')
        name = data.get('name', '')
        company = data.get('company', '')
        phone = data.get('phone', '')
        email = data.get('email', '')
        annualVolume = data.get('annualVolume', '')
        selectedQuestion = data.get('selectedQuestion', '')

        # Validate required fields
        if not name or not name.strip():
            logger.warning(f"Request [ID: {request_id}] - Name validation failed")
            return {'error': 'Name is required'}, 400

        if not company or not company.strip():
            logger.warning(f"Request [ID: {request_id}] - Company validation failed")
            return {'error': 'Company is required'}, 400

        # Validate phone
        is_valid, error_message = validate_phone(phone)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Phone validation failed: {error_message}")
            return {'error': error_message}, 400

        # Validate email
        is_valid, error_message = validate_email(email)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Email validation failed: {error_message}")
            return {'error': error_message}, 400

        # Validate message if provided
        if message:
            is_valid, error_message = validate_message(message)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Message validation failed: {error_message}")
                return {'error': error_message}, 400

        # Validate annual volume
        if not annualVolume or not annualVolume.strip():
            logger.warning(f"Request [ID: {request_id}] - Annual volume validation failed")
            return {'error': 'Annual volume is required'}, 400

        # Sanitize all inputs
        session_id = sanitize_input(session_id)
        message = sanitize_input(message)
        name = sanitize_input(name)
        company = sanitize_input(company)
        phone = sanitize_input(phone)
        email = sanitize_input(email)
        annualVolume = sanitize_input(annualVolume)
        selectedQuestion = sanitize_input(selectedQuestion)

        try:
            formatted_data = f"""
            Session ID: {session_id}
            Message: {message}
            Name: {name}
            Company: {company}
            Phone: {phone}
            Email: {email}
            Annual Volume: {annualVolume}
            Selected Question: {selectedQuestion}
            """

            email_client_instance.send_email(EMAIL_RECEIVER, "Got new Enquiry from tarrifIQ", formatted_data)
            response = f"Thank you for contacting us. We will get back to you soon."

            logger.info(f"Request [ID: {request_id}] - Contact form processed successfully")
            return {'message': response}, 200

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {'error': f"Error processing contact form: {str(e)}"}, 500


api.add_namespace(ns=ns)







# from flask import request, g
# from flask_restx import Namespace, Resource, fields
# from app import api
# from app.services import email_client_instance
# from app.config import EMAIL_RECEIVER
# ns = Namespace(name='contact', path="/v1", description='Contact API')


# @ns.route('/contact', methods=['POST'])
# class ContactAPI(Resource):
#     """
#         API resource for contact.
#     """
#     contact_form_model = ns.model('ContactForm', {
#         'session_id': fields.String(required=False, description='Session ID'),
#         'message': fields.String(required=False, description='Message'),
#         'name': fields.String(required=True, description='Name'),
#         'company': fields.String(required=True, description='Company'),
#         'phone': fields.String(required=True, description='Phone'),
#         'email': fields.String(required=True, description='Email'),
#         'annualVolume': fields.String(required=True, description='Annual Volume'),
#         'selectedQuestion': fields.String(required=False, description='Selected Question')
#     })

#     @ns.doc('contact_form')
#     @ns.expect(contact_form_model, validate=True)
#     def post(self):
#         """
#         Contact Api.
#         """
#         data = request.get_json()
#         session_id = data.get('session_id')
#         message = data.get('message')
#         name = data.get('name')
#         company = data.get('company')
#         phone = data.get('phone')
#         email = data.get('email')
#         annualVolume = data.get('annualVolume')
#         selectedQuestion = data.get('selectedQuestion')

#         formatted_data = f"""
#         Session ID: {session_id}
#         Message: {message}
#         Name: {name}
#         Company: {company}
#         Phone: {phone}
#         Email: {email}
#         Annual Volume: {annualVolume}
#         Selected Question: {selectedQuestion}
#         """
#         email_client_instance.send_email(EMAIL_RECEIVER, "Got new Enquiry from tarrifIQ", formatted_data)
#         response = f"Thank you for contacting us. We will get back to you soon."
#         return {'message': response}, 200


# api.add_namespace(ns=ns)
