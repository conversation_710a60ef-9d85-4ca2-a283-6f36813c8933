import pandas as pd
import sqlite3
from pathlib import Path
from sqlalchemy import create_engine
from app import db
import sys
import os
from sqlalchemy import  text
from langchain_community.agent_toolkits import create_sql_agent
from langchain_community.callbacks import StreamlitCallbackHandler
from langchain_community.utilities.sql_database import SQLDatabase
from langchain_community.agent_toolkits import create_sql_agent
from langchain_openai import Chat<PERSON>penA<PERSON>
from typing import List, Tuple
from app import server

from langchain_community.utilities import SQLDatabase
from sqlalchemy import create_engine

class SQLAgent:
    def __init__(self):
        RESOURCES_DIR = os.path.abspath(os.path.join(
            os.path.dirname(__file__), "../../../resources"))
        DB_PATH = os.path.join(RESOURCES_DIR, "HS_Summary.db")
        if os.path.exists(DB_PATH):
            self.engine = create_engine(
                f"sqlite:///{DB_PATH}", pool_pre_ping=True)
        else:
            raise FileNotFoundError(
                f"Database file '{DB_PATH}' not found.")

        db = SQLDatabase(engine=self.engine)
        print(db.dialect)
        print(db.get_usable_table_names())

        self.llm = ChatOpenAI(model="gpt-4o")
        self.agent_executor = create_sql_agent(
            self.llm, db=db, agent_type="openai-tools", verbose=True)
        

    def execute(self, query, hs_code, origin_country, llm_agent):
        if llm_agent == 1:
            # Usage
            response = self.agent_executor.invoke({"input": f"{query}"})
            return response
        else:
            results = self._get_top_fob_values( hs_code, origin_country)            
            # Print results
            if results:
                response = f"\nTop 20 countries by Import Quantity:\n" #FOB Value
                countr_Str = "Country".ljust(30)
                response += f"{countr_Str} Import Quantity\n"#FOB Value
                bar_str = "-" * 50
                response += f"{bar_str}\n"
                for country, value in results:
                    ctry_str = country.ljust(30)
                    response += f"{ctry_str} {value:,.2f}\n"   
                print(response)  
            else: 
                response = "No Data available for the specific hs_code in our internal database"
        return response
    
    def _get_top_fob_values(self, hs_code: int, origin_country: str) -> List[Tuple[str, float]]:
        """
        Retrieve top 10 countries by total FOB value for a given HS code, excluding a specific country.
        
        Args:
            db_path (str): Path to the SQLite database
            hs_code (int): HS code to filter by
            origin_country (str): Country to exclude from results
            
        Returns:
            List[Tuple[str, float]]: List of tuples containing (country, total_fob_value)
        """
        with server.app_context():
            with db.engine.connect() as conn:
                query_template = """
                    SELECT origin_country, SUM(total_std_qty) AS Value 
                    FROM trade_data_summary 
                    WHERE (
                        hs_code LIKE :hs_code_prefix OR 
                        hs_code LIKE :hs_code_trunc_8 OR 
                        hs_code LIKE :hs_code_trunc_6 OR
                        hs_code = :hs_code_exact
                    )
                    GROUP BY origin_country 
                    ORDER BY Value DESC 
                    LIMIT 20
                """   

                # Ensure hs_code is a string and construct search patterns dynamically
                hs_code_prefix = f"{hs_code}%"  # Match any longer HS codes starting with this
                hs_code_trunc_8 = f"{hs_code[:8]}%" if len(hs_code) > 8 else hs_code_prefix
                hs_code_trunc_6 = f"{hs_code[:6]}%" if len(hs_code) > 6 else hs_code_prefix
                hs_code_exact = hs_code  # Direct exact match

                # Parameters for query execution
                query_params = {
                    "hs_code_prefix": hs_code_prefix,
                    "hs_code_trunc_8": hs_code_trunc_8,
                    "hs_code_trunc_6": hs_code_trunc_6,
                    "hs_code_exact": hs_code_exact,
                }

                # Print query with actual values substituted
                formatted_query = query_template
                for key, value in query_params.items():
                    formatted_query = formatted_query.replace(f":{key}", f"'{value}'")

                #print("Generated SQL Query:\n", formatted_query)

                # Execute query using SQLAlchemy's text() and params
                result = conn.execute(text(query_template), query_params)

                # Fetch all results
                results = result.fetchall()
                print(results)
                if(len(results)==0):
                    return None
        return results