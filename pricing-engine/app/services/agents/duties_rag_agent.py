import os
import re
from typing import List, Dict, Optional, Tuple
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import CharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain_experimental.agents import create_pandas_dataframe_agent

class QueryParser:
    """Helper class to extract HTS numbers and country codes from queries"""
    
    @staticmethod
    def extract_hts_number(text: str) -> Optional[str]:
        """
        Extract HTS number from text using pattern matching.
        Supports formats like 0101.21.00, 010121.00, etc.
        """
        # Pattern for HTS numbers (adjust based on your specific format needs)
        patterns = [
            r'\b\d{4}\.\d{2}\.\d{2}\b',  # Format: 0101.21.00
            r'\b\d{6}\.\d{2}\b',          # Format: 010121.00
            r'\b\d{8}\b'                  # Format: 01012100
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                hts = match.group(0)
                # Standardize format to 0101.21.00
                if '.' not in hts:
                    hts = f"{hts[:4]}.{hts[4:6]}.{hts[6:]}"
                elif hts.count('.') == 1:
                    hts = f"{hts[:4]}.{hts[4:6]}.{hts[7:]}"
                return hts
        return None

    @staticmethod
    def extract_country_code(text: str) -> Optional[str]:
        """
        Extract country code from text.
        Looks for standard 2-letter country codes.
        """
        # Pattern for 2-letter country codes
        match = re.search(r'\b[A-Z]{2}\b', text.upper())
        return match.group(0) if match else None

    @staticmethod
    def extract_parameters(text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract both HTS number and country code from text.
        """
        hts_number = QueryParser.extract_hts_number(text)
        country_code = QueryParser.extract_country_code(text)
        return hts_number, country_code

class HTSDutyCalculator:
    """Helper class to calculate duties based on business rules"""
    
    @staticmethod
    def parse_duty_rate(rate_str: str) -> float:
        """Convert duty rate string to float value"""
        try:
            return float(rate_str.replace('%', '').strip())
        except (ValueError, AttributeError):
            return 0.0
    
    @staticmethod
    def check_country_eligibility(special_rate: str, country_code: str) -> bool:
        """Check if country code is eligible for special rate"""
        if pd.isna(special_rate) or pd.isna(country_code):
            return False
        return country_code in str(special_rate).split(',')
    
    @staticmethod
    def calculate_duties(
        general_rate: str,
        special_rate: str,
        column_2_rate: str,
        country_code: str
    ) -> Tuple[float, float]:
        """Calculate basic duty and anti-dumping duty based on business rules."""
        basic_duty = HTSDutyCalculator.parse_duty_rate(general_rate)
        is_special = HTSDutyCalculator.check_country_eligibility(special_rate, country_code)
        anti_dumping_duty = 0.0 if is_special else HTSDutyCalculator.parse_duty_rate(column_2_rate)
        return basic_duty, anti_dumping_duty

class HTSRagSystem:
    def __init__(self, csv_path: str):
        """Initialize the HTS RAG system with CSV data and OpenAI API key."""
        self.df = pd.read_csv(csv_path)
        self.setup_vectorstore()
        self.setup_agent()
        
    def get_duties_for_hts(self, hts_number: str, country_code: str) -> Dict:
        """Get duties for specific HTS number and country code."""
        row = self.df[self.df['HTS Number'] == hts_number]
        if row.empty:
            return {
                "error": f"HTS number {hts_number} not found",
                "basic_duty": 0.0,
                "anti_dumping_duty": 0.0
            }
            
        row = row.iloc[0]
        basic_duty, anti_dumping_duty = HTSDutyCalculator.calculate_duties(
            row['General Rate of Duty'],
            row['Special Rate of Duty'],
            row['Column 2 Rate of Duty'],
            country_code
        )
        
        return {
            "hts_number": hts_number,
            "country_code": country_code,
            "description": row['Description'],
            "basic_duty": basic_duty,
            "anti_dumping_duty": anti_dumping_duty,
            "total_duty": basic_duty + anti_dumping_duty
        }
        
    def setup_vectorstore(self):
        """Create FAISS vectorstore from CSV data for semantic search."""
        documents = []
        for _, row in self.df.iterrows():
            # Create smaller, focused documents for each aspect
            basic_info = (
                f"HTS Number: {row['HTS Number']}\n"
                f"Description: {row['Description']}\n"
            )
            
            duty_info = (
                f"HTS Number: {row['HTS Number']}\n"
                f"General Rate of Duty (Basic Duty): {row['General Rate of Duty']}\n"
            )
            
            special_duty_info = (
                f"HTS Number: {row['HTS Number']}\n"
                f"Special Rate of Duty (Countries exempt from Anti-Dumping): {row['Special Rate of Duty']}\n"
                f"Column 2 Rate of Duty (Anti-Dumping Duty Rate): {row['Column 2 Rate of Duty']}\n"
            )
            
            documents.extend([basic_info, duty_info, special_duty_info])
        
        text_splitter = CharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=150,
            separator="\n",
            length_function=len
        )
        texts = text_splitter.create_documents(documents)
        
        try:
            embeddings = OpenAIEmbeddings()
            self.vectorstore = FAISS.from_documents(texts, embeddings)
        except Exception as e:
            raise Exception(f"Error creating vectorstore: {str(e)}")
        
    def setup_agent(self):
        """Create pandas DataFrame agent for structured data analysis."""
        try:
            llm = ChatOpenAI(temperature=0, model="gpt-4")
            self.agent = create_pandas_dataframe_agent(
                llm,
                self.df,
                verbose=True,
                agent_type="openai-functions",
                handle_parsing_errors=True,
                allow_dangerous_code=True
            )
        except Exception as e:
            raise Exception(f"Error setting up agent: {str(e)}")
        
    def setup_qa_chain(self):
        """Create retrieval QA chain for semantic search queries."""
        llm = ChatOpenAI(temperature=0, model="gpt-4")
        
        template = """
        You are an expert in HTS (Harmonized Tariff Schedule) duties and tariffs.
        
        Business Rules for Duties:
        1. Basic Duty is always taken from the "General Rate of Duty" column
        2. Anti-Dumping Duty is taken from "Column 2 Rate of Duty" only if the country code is NOT in the "Special Rate of Duty" column
        3. Total Duty is the sum of Basic Duty and Anti-Dumping Duty (if applicable)
        
        Use the following pieces of context to answer the question at the end.
        If you don't know the answer, just say that you don't know.
        
        Context: {context}
        Question: {question}
        
        Answer:"""
        
        prompt = PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )
        
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=self.vectorstore.as_retriever(),
            chain_type_kwargs={"prompt": prompt},
            return_source_documents=True
        )
        
    def query(self, question: str) -> Dict:
        """
        Query the system about HTS duties with automatic parameter extraction.
        
        Args:
            question: User's question about HTS duties
            
        Returns:
            Dictionary containing answers and duty calculations
        """
        try:
            if not hasattr(self, 'qa_chain'):
                self.setup_qa_chain()
            
            # Extract HTS number and country code from question
            hts_number, country_code = QueryParser.extract_parameters(question)
            
            response = {
                "question": question,
                "extracted_parameters": {
                    "hts_number": hts_number,
                    "country_code": country_code
                },
                "qa_answer": None,
                "agent_answer": None,
                "duty_calculation": None
            }
            
            # Get semantic search answer
            qa_result = self.qa_chain({"query": question})
            response["qa_answer"] = qa_result["result"]
            
            # Get structured data answer
            agent_result = self.agent.run(question)
            response["agent_answer"] = agent_result
            
            # If parameters were extracted, calculate duties
            if hts_number and country_code:
                duty_info = self.get_duties_for_hts(hts_number, country_code)
                response["duty_calculation"] = duty_info
                
            return response
            
        except Exception as e:
            raise Exception(f"Error during query: {str(e)}")