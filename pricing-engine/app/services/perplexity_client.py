# import logging
# import requests

# from app.config import PERPLEXITY_API_URL, PERPLEXITY_API_KEY

# logger = logging.getLogger(__name__)

# class PerplexityClient:
#     def __init__(self):
#         pass

#     def query(self, payload: dict[str, any]):
#         """
#         Makes a request to the Perplexity API to get chat completion results
#         for the given query. Returns parsed JSON with the entire response.

#         Args:
#             query (str): The query string to ask Perplexity.
#             max_tokens (int): Max tokens in the generated response.

#         Returns:
#             dict: Parsed JSON response from Perplexity, which includes:
#                 - 'choices': a list of response objects 
#                 - 'citations': references for the answer
#                 - ... plus other metadata
#         """
#         headers = {
#             "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
#             "Content-Type": "application/json"
#         }

#         # You can adjust these parameters to your preference.
#         # payload = {
#         #     "model": "sonar-pro",
#         #     "messages": [
#         #         {
#         #             "role": "system",
#         #             "content": "Be precise and concise."
#         #         },
#         #         {
#         #             "role": "user",
#         #             "content": query
#         #         }
#         #     ],
#         #     "max_tokens": max_tokens,            # Optional limit on response length
#         #     "temperature": 0.2,
#         #     "top_p": 0.9,
#         #     # Adjust the domain filter if you want to restrict or broaden the search
#         #     "search_domain_filter": [],          # Example: ["perplexity.ai"] if you only want results from perplexity.ai
#         #     "return_images": False,
#         #     "return_related_questions": True,
#         #     "search_recency_filter": "day",
#         #     "top_k": 0,
#         #     "stream": False,
#         #     "presence_penalty": 0,
#         #     "frequency_penalty": 1,
#         #     "response_format": None
#         # }

#         logger.debug(f"called perplexity with payload: {payload}")

#         try:
#             response = requests.post(PERPLEXITY_API_URL, json=payload, headers=headers)
#             response.raise_for_status()
#             return response.json()
#         except requests.exceptions.RequestException as e:
#             logger.error(f"Error calling Perplexity API: {e}")
#             return {}


import logging
import requests
import json

from app.config import PERPLEXITY_API_URL, PERPLEXITY_API_KEY

logger = logging.getLogger(__name__)

class PerplexityClient:
    def __init__(self):
        pass

    def query(self, payload: dict[str, any]):
        """
        Makes a request to the Perplexity API to get chat completion results
        for the given query. Returns parsed JSON with the entire response.

        Args:
            query (str): The query string to ask Perplexity.
            max_tokens (int): Max tokens in the generated response.

        Returns:
            dict: Parsed JSON response from Perplexity, which includes:
                - 'choices': a list of response objects
                - 'citations': references for the answer
                - ... plus other metadata
        """
        headers = {
            "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }

        # You can adjust these parameters to your preference.
        # payload = {
        #     "model": "sonar-pro",
        #     "messages": [
        #         {
        #             "role": "system",
        #             "content": "Be precise and concise."
        #         },
        #         {
        #             "role": "user",
        #             "content": query
        #         }
        #     ],
        #     "max_tokens": max_tokens,            # Optional limit on response length
        #     "temperature": 0.2,
        #     "top_p": 0.9,
        #     # Adjust the domain filter if you want to restrict or broaden the search
        #     "search_domain_filter": [],          # Example: ["perplexity.ai"] if you only want results from perplexity.ai
        #     "return_images": False,
        #     "return_related_questions": True,
        #     "search_recency_filter": "day",
        #     "top_k": 0,
        #     "stream": False,
        #     "presence_penalty": 0,
        #     "frequency_penalty": 1,
        #     "response_format": None
        # }

        logger.debug(f"called perplexity with payload: {payload}")

        try:
            response = requests.post(PERPLEXITY_API_URL, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Perplexity API: {e}")
            return {}

    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> dict:
        """
        Get chemical information using Perplexity with structured prompting.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information
        """
        try:
            # Prepare prompt for Perplexity
            prompt = f"""You are a helpful assistant specializing in chemical information.
            When provided with a chemical name '{chemical_name}' and its application '{chemical_application}', return a JSON object with the product_name,
            product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code)
            and product_application (other common uses).
            In case of multiple hs codes, return only the most specific one.
            Format your response as valid JSON only with no additional text."""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a specialized chemical information expert that produces JSON responses. Return ONLY valid JSON with no additional explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.2
            }

            # Query Perplexity
            response = self.query(payload)

            # Extract content from response
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]

                # Try to parse JSON response
                try:
                    result = json.loads(content)

                    # Ensure all required fields exist
                    expected_fields = ["product_name", "product_family", "cas_number", "hs_code", "hts_number", "product_application"]
                    for field in expected_fields:
                        if field not in result:
                            result[field] = "Not found"

                    return result
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse Perplexity response as JSON: {content}")
                    return {
                        "product_name": chemical_name,
                        "product_family": "Error",
                        "cas_number": "Error",
                        "hs_code": "Error",
                        "hts_number": "Error",
                        "product_application": "Error"
                    }
            else:
                logger.error("No valid response from Perplexity")
                return {
                    "product_name": chemical_name,
                    "product_family": "Error",
                    "cas_number": "Error",
                    "hs_code": "Error",
                    "hts_number": "Error",
                    "product_application": "Error"
                }

        except Exception as e:
            logger.error(f"Error querying Perplexity: {str(e)}")
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": "Error"
            }
