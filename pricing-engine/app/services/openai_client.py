# from langchain_openai import ChatOpenAI
# from langchain_core.messages.base import BaseMessage

# class OpenAIClient:
#     def __init__(self, model: str = "o3-mini"):
#         self.llm = ChatOpenAI(model=model)
    
#     def query(self, messages: list[BaseMessage]) :
#         response = self.llm.invoke(messages)
#         return response.content


from langchain_openai import ChatOpenAI
from langchain_core.messages.base import BaseMessage
from langchain_core.messages import SystemMessage, HumanMessage
import json
import logging

logger = logging.getLogger(__name__)

class OpenAIClient:
    def __init__(self, model: str = "o3-mini"):
        self.llm = ChatOpenAI(model=model)

    def query(self, messages: list[BaseMessage]) :
        response = self.llm.invoke(messages)
        return response.content

    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> dict:
        """
        Get chemical information using OpenAI with structured prompting.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information
        """
        try:
            # Prepare system prompt for OpenAI
            system_prompt = """You are a helpful assistant specializing in chemical information.
            When provided with a chemical name, and its application, return a JSON object with the product_name,
            product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code)
            and product_application (other common uses).
            In case of multiple hs codes, return only the most specific one.
            Format your response as valid JSON only with no additional text."""

            user_prompt = f"Chemical name: {chemical_name}\nApplication: {chemical_application}"

            # Create messages for OpenAI
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            # Query OpenAI
            response = self.query(messages)

            # Clean up response if it contains markdown code blocks
            response = response.removeprefix("```json")
            response = response.removesuffix("```")

            # Try to parse JSON response
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse OpenAI response as JSON: {response}")
                return {
                    "product_name": chemical_name,
                    "product_family": "Error",
                    "cas_number": "Error",
                    "hs_code": "Error",
                    "hts_number": "Error",
                    "product_application": "Error"
                }

        except Exception as e:
            logger.error(f"Error querying OpenAI: {str(e)}")
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": "Error"
            }
