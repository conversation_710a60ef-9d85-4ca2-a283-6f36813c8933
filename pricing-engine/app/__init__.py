import logging

from flask import Flask
from flask_cors import CORS
from flask_migrate import Migrate
from flask_restx import Api
from flask_sqlalchemy import SQLAlchemy
from prometheus_flask_exporter import PrometheusMetrics

from app.config import SERVICE_PREFIX

server = Flask(__name__)
server.logger.setLevel(logging.DEBUG)
metrics = PrometheusMetrics(server)
metrics.info('pricing_engine', 'pricing_engine', version='1.0.0')

api = Api(
    server,
    prefix=f"/{SERVICE_PREFIX}",
    doc=f"/{SERVICE_PREFIX}/docs",  # Change Swagger UI endpoint to /docs
    title="Pricing Engine API",
    version="1.0.0",
    description="Pricing Engine API Documentation",
)
CORS(server,
     origins=['*'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=[
         'Content-Type',
         'Authorization',
         'X-Requested-With',
         'Accept',
         'Origin'
     ],
     supports_credentials=True)
server.config.from_pyfile('config.py')
db = SQLAlchemy(server)


# Import all entities to create the tables
from app.entities.chat_history import ChatHistory

migrate = Migrate(server, db)

from app.controllers import (
    chat_controller,
    contact_controller,
    code_resolver_controller,
    trade_finder_controller,
    trade_ranker_controller,
    health_controller
)
