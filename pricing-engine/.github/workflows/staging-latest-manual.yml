name: staging-manual-latest-workflow  # Manual Deployment Workflow

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to deploy"
        required: true
        default: "main"

permissions:
  id-token: write   # Needed for OIDC authentication
  contents: read    # Required to read repo contents

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    environment: stg

    outputs:
      image_tag: ${{ steps.export_image_tag.outputs.image_tag }}
      image_exists: ${{ steps.check_ecr.outputs.image_exists }}

    steps:
      - name: Checkout specified branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Configure AWS credentials (ECR Account)
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::************:role/GitHubECRRole
          role-session-name: GitHubECRSession
          aws-region: us-east-1

      - name: Extract commit SHA
        run: echo "IMAGE_TAG=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
        shell: bash

      - name: Export IMAGE_TAG as output
        id: export_image_tag
        run: echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
        shell: bash

      - name: Check if Docker image already exists in ECR
        id: check_ecr
        env:
          AWS_REGION: us-east-1
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
        run: |
          IMAGE_EXISTS=$(aws ecr describe-images \
            --repository-name pricing-engine \
            --image-ids imageTag=$IMAGE_TAG \
            --region $AWS_REGION \
            --query 'imageDetails[0].imageTags[0]' \
            --output text 2>/dev/null || echo "not_found")

          echo "Image exists: $IMAGE_EXISTS"
          if [ "$IMAGE_EXISTS" != "not_found" ]; then
            echo "image_exists=true" >> $GITHUB_OUTPUT
          else
            echo "image_exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Login to Amazon ECR
        if: steps.check_ecr.outputs.image_exists == 'false'
        uses: aws-actions/amazon-ecr-login@v2

      - name: Git LFS Install & Pull
        if: steps.check_ecr.outputs.image_exists == 'false'
        run: |
          git lfs install
          git lfs pull

      - name: Build and Push Docker Image
        if: steps.check_ecr.outputs.image_exists == 'false'
        env:
          ECR_REGISTRY: ************.dkr.ecr.us-east-1.amazonaws.com
          IMAGE_NAME: pricing-engine
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
        run: |
          docker buildx create --use
          docker buildx inspect --bootstrap
          docker buildx build \
            --platform linux/arm64 \
            --tag $ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG \
            --push .

  deploy-to-eks:
    name: Deploy to EKS
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: stg

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials (EKS Account)
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::************:role/GitHubEKSRole
          role-session-name: GitHubEKSSession
          aws-region: us-west-2

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name mstack-staging-eks-cluster --region us-west-2

      - name: Deploy to Kubernetes
        env:
          ECR_REGISTRY: ************.dkr.ecr.us-east-1.amazonaws.com
          IMAGE_NAME: pricing-engine
          IMAGE_TAG: ${{ needs.build-and-push.outputs.image_tag }}
        run: |
          kubectl set image deployment/pricing-engine pricing-engine=$ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG -n apis
      - name: Inject environment variables into deployment
        run: |
          kubectl set env deployment/pricing-engine \
          AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}" \
          AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}" \
          DATABASE_URL="${{ secrets.DATABASE_URL }}" \
          OPENAI_API_KEY="${{ secrets.OPENAI_API_KEY }}" \
          PERPLEXITY_API_KEY="${{ secrets.PERPLEXITY_API_KEY }}" \
          AWS_REGION="${{ vars.AWS_REGION }}" \
          EMAIL_RECEIVER="${{ vars.EMAIL_RECEIVER }}" \
          EMAIL_SENDER="${{ vars.EMAIL_SENDER }}" \
          -n apis



      - name: Wait for new pod and stream logs (60 seconds)
        env:
          NAMESPACE: apis
          DEPLOYMENT_NAME: pricing-engine
        run: |
          echo "Waiting for pod from deployment $DEPLOYMENT_NAME in namespace $NAMESPACE..."
          sleep 15
          POD_NAME=""
          for i in {1..30}; do
            POD_LIST=$(kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[*].metadata.name}')
            for POD in $POD_LIST; do
              PHASE=$(kubectl get pod -n $NAMESPACE "$POD" -o jsonpath='{.status.phase}')
              if [[ "$PHASE" == "Running" || "$PHASE" == "Pending" ]]; then
                POD_NAME=$POD
              fi
            done
            
            if [[ ! -z "$POD_NAME" ]]; then
              echo "✅ Found latest pod in Running or Pending state: $POD_NAME"
              POD_AGE=$(kubectl get pod -n $NAMESPACE "$POD_NAME" --no-headers | awk '{print $5}')
              echo "📅 Pod $POD_NAME is $POD_AGE old"
              break
            fi
          
            echo "⌛ Waiting for a pod... ($i/30)"
            sleep 5
          done
          
          if [[ -z "$POD_NAME" ]]; then
            echo "❌ Failed to find a pod for deployment $DEPLOYMENT_NAME"
            exit 1
          fi
          
          echo "📺 Streaming logs from pod: $POD_NAME for 60 seconds"
          sleep 60
          
          timeout 60 kubectl logs -n $NAMESPACE $POD_NAME -f || echo "⚠️ Log streaming completed or pod exited"
