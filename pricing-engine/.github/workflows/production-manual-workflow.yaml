name: production-manual-workflow  # Manual Deployment Workflow for Production

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to deploy"
        required: true
        default: "main"

permissions:
  id-token: write   # Needed for OIDC authentication
  contents: read    # Required to read repo contents

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest

    outputs:
      image_tag: ${{ steps.export_image_tag.outputs.image_tag }}

    steps:
      - name: Checkout specified branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Configure AWS credentials (ECR Account)
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::************:role/GitHubECRRole
          role-session-name: GitHubECRSession
          aws-region: us-east-1  # Change if needed

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Extract commit SHA
        run: echo "IMAGE_TAG=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
        shell: bash

      - name: Export IMAGE_TAG as output
        id: export_image_tag  # Unique ID to avoid duplication
        run: echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
        shell: bash

      - name: Git LFS Install & Pull
        run: |
          git lfs install
          git lfs pull

      - name: Build and Push Docker Image
        env:
          ECR_REGISTRY: ************.dkr.ecr.us-east-1.amazonaws.com
          IMAGE_NAME: pricing-engine
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
        run: |
          docker buildx create --use
          docker buildx inspect --bootstrap
          docker buildx build \
            --platform linux/arm64 \
            --tag $ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG \
            --push .

  deploy-to-eks:
    name: Deploy to EKS
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials (EKS Account)
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::************:role/GitHubProdRole
          role-session-name: GitHubEKSSession
          aws-region: us-west-2  # Change if needed

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name mstack-production-eks-cluster --region us-west-2

      - name: Deploy to Kubernetes
        env:
          ECR_REGISTRY: ************.dkr.ecr.us-east-1.amazonaws.com
          IMAGE_NAME: pricing-engine
          IMAGE_TAG: ${{ needs.build-and-push.outputs.image_tag }}
        run: |
          kubectl set image deployment/pricing-engine pricing-engine=$ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG -n apis
