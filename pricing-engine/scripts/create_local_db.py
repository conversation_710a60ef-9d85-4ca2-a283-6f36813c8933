import getpass
import os
import requests
from dotenv import load_dotenv
import pandas as pd

load_dotenv()  # take environment variables from .env.
#
df = pd.read_parquet("sample_volza_trade_data_summary.parquet")
print(df.shape)
#print(df.columns.tolist())

from langchain_community.utilities import SQLDatabase
from sqlalchemy import create_engine

#engine = create_engine("sqlite:///HS_samples.db")
engine = create_engine("sqlite:///HS_Summary.db")
df.to_sql("HS_Summary", engine, index=False)

db = SQLDatabase(engine=engine)
print(db.dialect)
print(db.get_usable_table_names())