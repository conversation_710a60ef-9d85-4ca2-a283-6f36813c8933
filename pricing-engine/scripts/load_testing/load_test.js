import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
    stages: [
        { duration: '20s', target: 100 }, // Ramp up to 20 users over 1 minute
        { duration: '40s', target: 100 }, // Stay at 20 users for 3 minutes
        { duration: '20s', target: 0 },  // Ramp down to 0 users over 1 minute
    ],
};

const BASE_URL = 'http://127.0.0.1:5001/v1';

function chat() {
    const payload = JSON.stringify({
        "session_id": "123",
        "message": "Hi",
    });

    const params = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    let response = http.post(`${BASE_URL}/chat`, payload, params);
    check(response, { 'Created event successfully': (r) => r.status === 201 });
    return response.json();
}

export default function () {
    const response  = chat();
}