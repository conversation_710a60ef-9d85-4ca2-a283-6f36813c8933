#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to explore the database and understand the data structure.
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from pricing-engine
load_dotenv('pricing-engine/.env')

def explore_database():
    """Explore the database structure and data."""
    print("=" * 80)
    print("DATABASE EXPLORATION")
    print("=" * 80)
    
    # Database configuration from pricing-engine
    db_config = {
        'host': os.environ.get('DB_HOST'),
        'database': os.environ.get('DB_NAME'),
        'user': os.environ.get('DB_USER'),
        'password': os.environ.get('DB_PASSWORD'),
        'port': int(os.environ.get('DB_PORT', 5432))
    }
    
    print(f"Database config: {db_config}")
    
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 1. Check if volza_trade_data table exists
        print("\n1. Checking table existence:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%volza%' OR table_name LIKE '%trade%'
        """)
        tables = cursor.fetchall()
        print(f"   Trade-related tables: {[t[0] for t in tables]}")
        
        # 2. Get table structure
        print("\n2. Table structure:")
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'volza_trade_data'
            ORDER BY ordinal_position
        """)
        columns = cursor.fetchall()
        if columns:
            print("   volza_trade_data columns:")
            for col_name, col_type in columns:
                print(f"     {col_name}: {col_type}")
        else:
            print("   volza_trade_data table not found")
        
        # 3. Check total row count
        print("\n3. Data volume:")
        try:
            cursor.execute("SELECT COUNT(*) FROM volza_trade_data")
            total_rows = cursor.fetchone()[0]
            print(f"   Total rows in volza_trade_data: {total_rows:,}")
        except Exception as e:
            print(f"   Error counting rows: {e}")
        
        # 4. Check HS code patterns
        print("\n4. HS Code analysis:")
        try:
            cursor.execute("""
                SELECT SUBSTRING(hs_code, 1, 4) as hs4, COUNT(*) as count
                FROM volza_trade_data 
                WHERE hs_code IS NOT NULL
                GROUP BY SUBSTRING(hs_code, 1, 4)
                ORDER BY count DESC
                LIMIT 10
            """)
            hs_codes = cursor.fetchall()
            print("   Top 10 HS codes (first 4 digits):")
            for hs4, count in hs_codes:
                print(f"     {hs4}: {count:,} records")
                
            # Check specifically for 3907
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE SUBSTRING(hs_code, 1, 4) = '3907'
            """)
            count_3907 = cursor.fetchone()[0]
            print(f"   Records with HS code 3907*: {count_3907:,}")
            
        except Exception as e:
            print(f"   Error analyzing HS codes: {e}")
        
        # 5. Check destinations
        print("\n5. Destination analysis:")
        try:
            cursor.execute("""
                SELECT parent_cod, COUNT(*) as count
                FROM volza_trade_data 
                WHERE parent_cod IS NOT NULL
                GROUP BY parent_cod
                ORDER BY count DESC
                LIMIT 10
            """)
            destinations = cursor.fetchall()
            print("   Top 10 destinations:")
            for dest, count in destinations:
                print(f"     {dest}: {count:,} records")
                
            # Check specifically for United States
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE parent_cod = 'United States'
            """)
            count_us = cursor.fetchone()[0]
            print(f"   Records with destination 'United States': {count_us:,}")
            
        except Exception as e:
            print(f"   Error analyzing destinations: {e}")
        
        # 6. Check product descriptions for Xanthan
        print("\n6. Product description analysis:")
        try:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE product_desc ILIKE '%xanthan%'
            """)
            count_xanthan = cursor.fetchone()[0]
            print(f"   Records with 'xanthan' in product_desc: {count_xanthan:,}")
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE product_desc ILIKE '%gum%'
            """)
            count_gum = cursor.fetchone()[0]
            print(f"   Records with 'gum' in product_desc: {count_gum:,}")
            
        except Exception as e:
            print(f"   Error analyzing product descriptions: {e}")
        
        # 7. Check date range
        print("\n7. Date range analysis:")
        try:
            cursor.execute("""
                SELECT MIN(date) as min_date, MAX(date) as max_date, COUNT(*) as total
                FROM volza_trade_data 
                WHERE date IS NOT NULL
            """)
            date_info = cursor.fetchone()
            if date_info:
                min_date, max_date, total = date_info
                print(f"   Date range: {min_date} to {max_date}")
                print(f"   Records with dates: {total:,}")
                
                # Check recent data (last 12 months)
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM volza_trade_data 
                    WHERE date >= CURRENT_DATE - INTERVAL '12 month'
                """)
                recent_count = cursor.fetchone()[0]
                print(f"   Records in last 12 months: {recent_count:,}")
                
        except Exception as e:
            print(f"   Error analyzing dates: {e}")
        
        # 8. Test the exact query conditions
        print("\n8. Testing exact query conditions:")
        try:
            # Test each condition separately
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE parent_cod = 'United States'
                AND SUBSTRING(hs_code, 1, 4) = '3907'
            """)
            count_hs_dest = cursor.fetchone()[0]
            print(f"   HS 3907* + US destination: {count_hs_dest:,}")
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE parent_cod = 'United States'
                AND SUBSTRING(hs_code, 1, 4) = '3907'
                AND date >= CURRENT_DATE - INTERVAL '12 month'
            """)
            count_hs_dest_date = cursor.fetchone()[0]
            print(f"   HS 3907* + US + last 12 months: {count_hs_dest_date:,}")
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM volza_trade_data 
                WHERE parent_cod = 'United States'
                AND SUBSTRING(hs_code, 1, 4) = '3907'
                AND date >= CURRENT_DATE - INTERVAL '12 month'
                AND (product_desc ILIKE '%xanthan%' OR product_desc ILIKE '%gum%')
            """)
            count_full = cursor.fetchone()[0]
            print(f"   Full query conditions: {count_full:,}")
            
        except Exception as e:
            print(f"   Error testing query conditions: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Database connection failed: {e}")

if __name__ == "__main__":
    explore_database()
