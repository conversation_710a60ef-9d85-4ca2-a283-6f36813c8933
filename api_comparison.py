#!/usr/bin/env python3
"""
Script to compare the external API (************:8080) with the local pricing-engine API
and analyze SQL queries being executed.
"""

import requests
import json
import urllib.parse
import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from pricing-engine
load_dotenv('pricing-engine/.env')

def test_external_api():
    """Test the external API that procure-intelligence calls."""
    print("=" * 80)
    print("EXTERNAL API TEST (************:8080)")
    print("=" * 80)
    
    # Test parameters from your example
    params = {
        "hs_code": "39073000",
        "destination": "United States",
        "months": 12,
        "chemical_name": "<PERSON><PERSON><PERSON>"
    }
    
    base_url = "http://************:8080/api/top_suppliers_by_geography_tariff"
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"
    
    print(f"Testing URL: {api_url}")
    
    try:
        response = requests.get(api_url, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response Type: {type(data)}")
                print(f"Response Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict) and 'result' in data:
                    result_data = data['result'].get('data', [])
                    print(f"Number of results: {len(result_data)}")
                    
                    if result_data:
                        print("\nFirst result:")
                        first_result = result_data[0]
                        for key, value in first_result.items():
                            print(f"  {key}: {value}")
                        
                        print(f"\nAll countries in results:")
                        for i, item in enumerate(result_data[:10]):  # Show first 10
                            country = item.get('origin_country', 'Unknown')
                            quantity = item.get('total_quantity', 0)
                            fob = item.get('average_fob', 0)
                            print(f"  {i+1}. {country}: qty={quantity}, fob=${fob}")
                
                return data
                
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}...")
                return None
        else:
            print(f"Error response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def test_local_pricing_engine():
    """Test the local pricing-engine API."""
    print("\n" + "=" * 80)
    print("LOCAL PRICING-ENGINE API TEST (127.0.0.1:5001)")
    print("=" * 80)
    
    params = {
        "hs_code": "39073000",
        "destination": "United States",
        "months": 12,
        "chemical_name": "Xanthan Gum"
    }
    
    base_url = "http://127.0.0.1:5001/pricing/v1/trade/top-suppliers-by-geography-tariff"
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"
    
    print(f"Testing URL: {api_url}")
    
    try:
        response = requests.get(api_url, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response Type: {type(data)}")
                print(f"Response Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict) and 'result' in data:
                    result_data = data['result'].get('data', [])
                    print(f"Number of results: {len(result_data)}")
                    
                    if result_data:
                        print("\nFirst result:")
                        first_result = result_data[0]
                        for key, value in first_result.items():
                            print(f"  {key}: {value}")
                        
                        print(f"\nAll countries in results:")
                        for i, item in enumerate(result_data[:10]):  # Show first 10
                            country = item.get('origin_country', 'Unknown')
                            quantity = item.get('total_quantity', 0)
                            fob = item.get('average_fob', 0)
                            print(f"  {i+1}. {country}: qty={quantity}, fob=${fob}")
                
                return data
                
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}...")
                return None
        else:
            print(f"Error response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def test_database_query():
    """Test the actual database query that should be executed."""
    print("\n" + "=" * 80)
    print("DIRECT DATABASE QUERY TEST")
    print("=" * 80)
    
    # Database configuration from pricing-engine
    db_config = {
        'host': os.environ.get('DB_HOST'),
        'database': os.environ.get('DB_NAME'),
        'user': os.environ.get('DB_USER'),
        'password': os.environ.get('DB_PASSWORD'),
        'port': int(os.environ.get('DB_PORT', 5432))
    }
    
    print(f"Database config: {db_config}")
    
    # The actual query that should be executed (from pricing-engine code)
    query = """
    WITH filtered_by_hs AS (
        SELECT
            parent_coo,
            global_std_unit_id,
            std_qty,
            fob_value_usd,
            date,
            product_desc,
            gross_wt,
            hs_code
        FROM
            volza_trade_data
        WHERE
            parent_cod = %s
            AND date >= CURRENT_DATE - INTERVAL '%s month'
            AND (product_desc ILIKE %s OR product_desc ILIKE %s)
            AND SUBSTRING(hs_code, 1, 4) = %s
    ),
    aggregated AS (
        SELECT
            parent_coo AS origin_country,
            global_std_unit_id,
            SUM(std_qty) AS total_quantity,
            AVG(fob_value_usd) AS average_fob,
            MIN(fob_value_usd) AS minimum_fob,
            COUNT(*) AS shipment_count,
            SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
            AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
        FROM
            filtered_by_hs
        WHERE
            CASE
                WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
                    GREATEST(gross_wt::INTEGER, std_qty) >= 10
                WHEN gross_wt ~ '^[0-9]+$' THEN
                    gross_wt::INTEGER >= 10
                WHEN std_qty IS NOT NULL THEN
                    std_qty >= 10
                ELSE FALSE
            END
        GROUP BY
            parent_coo, global_std_unit_id
        HAVING
            COUNT(*) >= 1
        ORDER BY
            total_quantity DESC
    ),
    ranked AS (
        SELECT *,
            ROW_NUMBER() OVER (PARTITION BY origin_country ORDER BY total_quantity DESC) AS rn
        FROM aggregated
    )
    SELECT *
    FROM ranked
    WHERE rn = 1
    ORDER BY total_quantity DESC;
    """
    
    params = ('United States', 12, '%Xanthan%', '%Gum%', '3907')
    
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        print("Executing query...")
        cursor.execute(query, params)
        
        # Get column names
        column_names = [desc[0] for desc in cursor.description]
        results = cursor.fetchall()
        
        print(f"Query returned {len(results)} rows")
        print(f"Columns: {column_names}")
        
        if results:
            print("\nFirst 5 results:")
            for i, row in enumerate(results[:5]):
                row_dict = dict(zip(column_names, row))
                country = row_dict.get('origin_country', 'Unknown')
                quantity = row_dict.get('total_quantity', 0)
                fob = row_dict.get('average_fob', 0)
                print(f"  {i+1}. {country}: qty={quantity}, fob=${fob}")
        
        cursor.close()
        conn.close()
        
        return results
        
    except Exception as e:
        print(f"Database query failed: {e}")
        return None

def compare_results(external_data, local_data, db_results):
    """Compare results from all three sources."""
    print("\n" + "=" * 80)
    print("COMPARISON SUMMARY")
    print("=" * 80)
    
    print("Data Sources:")
    print(f"  External API (************:8080): {'✓' if external_data else '✗'}")
    print(f"  Local API (127.0.0.1:5001): {'✓' if local_data else '✗'}")
    print(f"  Direct DB Query: {'✓' if db_results else '✗'}")
    
    # Extract country lists for comparison
    if external_data and isinstance(external_data, dict) and 'result' in external_data:
        external_countries = [item.get('origin_country') for item in external_data['result'].get('data', [])]
    else:
        external_countries = []
    
    if local_data and isinstance(local_data, dict) and 'result' in local_data:
        local_countries = [item.get('origin_country') for item in local_data['result'].get('data', [])]
    else:
        local_countries = []
    
    print(f"\nCountry comparison:")
    print(f"  External API countries: {external_countries[:5]}...")
    print(f"  Local API countries: {local_countries[:5]}...")
    
    if external_countries and local_countries:
        if external_countries == local_countries:
            print("  ✓ Country order is identical")
        else:
            print("  ⚠ Country order differs")
            print(f"    External: {external_countries}")
            print(f"    Local: {local_countries}")

if __name__ == "__main__":
    # Test all three data sources
    external_data = test_external_api()
    local_data = test_local_pricing_engine()
    db_results = test_database_query()
    
    # Compare results
    compare_results(external_data, local_data, db_results)
